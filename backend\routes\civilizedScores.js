const express = require('express');
const router = express.Router();
const { getCivilizedScores, createCivilizedScore, updateCivilizedScore, deleteCivilizedScore } = require('../controllers/civilizedScoreController');
const { authenticateToken, requireDormAdmin } = require('../middleware/auth');

// 获取文明宿舍评分列表
router.get('/', authenticateToken, getCivilizedScores);

// 创建文明宿舍评分 (需要宿舍管理员或系统管理员权限)
router.post('/', authenticateToken, requireDormAdmin, createCivilizedScore);

// 更新文明宿舍评分 (需要宿舍管理员或系统管理员权限)
router.put('/:id', authenticateToken, requireDormAdmin, updateCivilizedScore);

// 删除文明宿舍评分 (需要宿舍管理员或系统管理员权限)
router.delete('/:id', authenticateToken, requireDormAdmin, deleteCivilizedScore);

module.exports = router;
