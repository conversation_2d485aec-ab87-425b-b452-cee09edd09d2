const express = require('express');
const router = express.Router();
const { getVisitors, createVisitor, updateVisitor, deleteVisitor } = require('../controllers/visitorController');
const { authenticateToken, requireDormAdmin } = require('../middleware/auth');

// 获取访客记录列表
router.get('/', authenticateToken, getVisitors);

// 创建访客记录 (需要宿舍管理员或系统管理员权限)
router.post('/', authenticateToken, requireDormAdmin, createVisitor);

// 更新访客记录 (需要宿舍管理员或系统管理员权限)
router.put('/:id', authenticateToken, requireDormAdmin, updateVisitor);

// 删除访客记录 (需要宿舍管理员或系统管理员权限)
router.delete('/:id', authenticateToken, requireDormAdmin, deleteVisitor);

module.exports = router;
