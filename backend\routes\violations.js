const express = require('express');
const router = express.Router();
const { getViolations, createViolation, updateViolation, deleteViolation } = require('../controllers/violationController');
const { authenticateToken, requireDormAdmin } = require('../middleware/auth');

// 获取违规记录列表
router.get('/', authenticateToken, getViolations);

// 创建违规记录 (需要宿舍管理员或系统管理员权限)
router.post('/', authenticateToken, requireDormAdmin, createViolation);

// 更新违规记录 (需要宿舍管理员或系统管理员权限)
router.put('/:id', authenticateToken, requireDormAdmin, updateViolation);

// 删除违规记录 (需要宿舍管理员或系统管理员权限)
router.delete('/:id', authenticateToken, requireDormAdmin, deleteViolation);

module.exports = router;
