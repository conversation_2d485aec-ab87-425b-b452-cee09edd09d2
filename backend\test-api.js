// 简单的API测试脚本
// 使用Node.js内置的fetch (Node.js 18+)

const API_BASE_URL = 'http://localhost:3002/api';

async function testAPI() {
  console.log('🧪 开始测试API...\n');

  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ 健康检查:', healthData.message);

    // 2. 测试登录
    console.log('\n2. 测试登录...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        role: '系统管理员',
        password: 'password123'
      }),
    });
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ 登录成功:', loginData.data.user.name);
      
      const token = loginData.data.token;

      // 3. 测试获取学院列表
      console.log('\n3. 测试获取学院列表...');
      const collegesResponse = await fetch(`${API_BASE_URL}/colleges`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (collegesResponse.ok) {
        const collegesData = await collegesResponse.json();
        console.log('✅ 学院列表:', collegesData.data.colleges.length + '个学院');
      } else {
        console.log('❌ 获取学院列表失败');
      }

      // 4. 测试获取用户列表
      console.log('\n4. 测试获取用户列表...');
      const usersResponse = await fetch(`${API_BASE_URL}/users`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        console.log('✅ 用户列表:', usersData.data.users.length + '个用户');
      } else {
        console.log('❌ 获取用户列表失败');
      }

      // 5. 测试获取宿舍楼列表
      console.log('\n5. 测试获取宿舍楼列表...');
      const buildingsResponse = await fetch(`${API_BASE_URL}/dorm-buildings`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (buildingsResponse.ok) {
        const buildingsData = await buildingsResponse.json();
        console.log('✅ 宿舍楼列表:', buildingsData.data.dormBuildings.length + '栋宿舍楼');
      } else {
        console.log('❌ 获取宿舍楼列表失败');
      }

    } else {
      const loginError = await loginResponse.json();
      console.log('❌ 登录失败:', loginError.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }

  console.log('\n🏁 API测试完成');
}

// 运行测试
testAPI();
