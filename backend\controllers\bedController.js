const { query, transaction } = require('../config/database');

/**
 * 获取床位列表
 */
async function getBeds(req, res) {
  try {
    const { roomId, dormBuildingId, status } = req.query;
    
    let sql = `
      SELECT b.*, r.room_number, r.dorm_building_id, db.name as dorm_building_name, u.name as student_name
      FROM beds b
      LEFT JOIN rooms r ON b.room_id = r.id
      LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
      LEFT JOIN users u ON b.student_id = u.id
      WHERE 1=1
    `;
    const params = [];

    if (roomId) {
      sql += ' AND b.room_id = ?';
      params.push(roomId);
    }

    if (dormBuildingId) {
      sql += ' AND r.dorm_building_id = ?';
      params.push(dormBuildingId);
    }

    if (status) {
      sql += ' AND b.status = ?';
      params.push(status);
    }

    sql += ' ORDER BY r.dorm_building_id, r.room_number, CAST(b.bed_number AS UNSIGNED)';

    const beds = await query(sql, params);

    res.json({
      success: true,
      data: {
        beds: beds
      }
    });
  } catch (error) {
    console.error('获取床位列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 分配床位给学生
 */
async function assignBed(req, res) {
  try {
    const { id } = req.params;
    const { student_id } = req.body;

    // 验证输入
    if (!student_id) {
      return res.status(400).json({
        success: false,
        message: '学生ID是必填项'
      });
    }

    await transaction(async (connection) => {
      // 检查床位是否存在且空闲
      const [beds] = await connection.execute(
        'SELECT * FROM beds WHERE id = ? AND status = ?',
        [id, '空闲']
      );

      if (beds.length === 0) {
        throw new Error('床位不存在或已被占用');
      }

      const bed = beds[0];

      // 检查学生是否存在且为学生角色
      const [students] = await connection.execute(
        'SELECT * FROM users WHERE id = ? AND role = ?',
        [student_id, '学生']
      );

      if (students.length === 0) {
        throw new Error('学生不存在或角色不正确');
      }

      const student = students[0];

      // 检查学生是否已经有床位
      const [existingBeds] = await connection.execute(
        'SELECT id FROM beds WHERE student_id = ?',
        [student_id]
      );

      if (existingBeds.length > 0) {
        throw new Error('该学生已经分配了床位');
      }

      // 获取房间信息
      const [rooms] = await connection.execute(
        'SELECT r.*, db.name as dorm_building_name FROM rooms r LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id WHERE r.id = ?',
        [bed.room_id]
      );

      const room = rooms[0];

      // 分配床位
      await connection.execute(
        'UPDATE beds SET status = ?, student_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['已入住', student_id, id]
      );

      // 更新学生的宿舍信息
      await connection.execute(
        'UPDATE users SET dorm_building_id = ?, room_number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [room.dorm_building_id, room.room_number, student_id]
      );

      // 更新房间的已住人数
      await connection.execute(
        'UPDATE rooms SET occupied_beds = occupied_beds + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [bed.room_id]
      );
    });

    // 获取更新后的床位信息
    const updatedBeds = await query(
      `SELECT b.*, r.room_number, r.dorm_building_id, db.name as dorm_building_name, u.name as student_name
       FROM beds b
       LEFT JOIN rooms r ON b.room_id = r.id
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       LEFT JOIN users u ON b.student_id = u.id
       WHERE b.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '床位分配成功',
      data: {
        bed: updatedBeds[0]
      }
    });
  } catch (error) {
    console.error('分配床位错误:', error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器内部错误'
    });
  }
}

/**
 * 释放床位
 */
async function releaseBed(req, res) {
  try {
    const { id } = req.params;

    await transaction(async (connection) => {
      // 检查床位是否存在且已被占用
      const [beds] = await connection.execute(
        'SELECT * FROM beds WHERE id = ? AND status = ?',
        [id, '已入住']
      );

      if (beds.length === 0) {
        throw new Error('床位不存在或未被占用');
      }

      const bed = beds[0];

      // 释放床位
      await connection.execute(
        'UPDATE beds SET status = ?, student_id = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['空闲', id]
      );

      // 清除学生的宿舍信息
      if (bed.student_id) {
        await connection.execute(
          'UPDATE users SET dorm_building_id = NULL, room_number = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [bed.student_id]
        );
      }

      // 更新房间的已住人数
      await connection.execute(
        'UPDATE rooms SET occupied_beds = occupied_beds - 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [bed.room_id]
      );
    });

    // 获取更新后的床位信息
    const updatedBeds = await query(
      `SELECT b.*, r.room_number, r.dorm_building_id, db.name as dorm_building_name, u.name as student_name
       FROM beds b
       LEFT JOIN rooms r ON b.room_id = r.id
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       LEFT JOIN users u ON b.student_id = u.id
       WHERE b.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '床位释放成功',
      data: {
        bed: updatedBeds[0]
      }
    });
  } catch (error) {
    console.error('释放床位错误:', error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器内部错误'
    });
  }
}

/**
 * 调换床位
 */
async function swapBeds(req, res) {
  try {
    const { bed1_id, bed2_id } = req.body;

    // 验证输入
    if (!bed1_id || !bed2_id) {
      return res.status(400).json({
        success: false,
        message: '需要提供两个床位ID'
      });
    }

    if (bed1_id === bed2_id) {
      return res.status(400).json({
        success: false,
        message: '不能调换相同的床位'
      });
    }

    await transaction(async (connection) => {
      // 获取两个床位的信息
      const [beds] = await connection.execute(
        'SELECT * FROM beds WHERE id IN (?, ?)',
        [bed1_id, bed2_id]
      );

      if (beds.length !== 2) {
        throw new Error('床位不存在');
      }

      const bed1 = beds.find(b => b.id === bed1_id);
      const bed2 = beds.find(b => b.id === bed2_id);

      // 交换床位的学生
      await connection.execute(
        'UPDATE beds SET student_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [bed2.student_id, bed1_id]
      );

      await connection.execute(
        'UPDATE beds SET student_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [bed1.student_id, bed2_id]
      );

      // 更新学生的房间信息
      if (bed1.student_id) {
        const [room2] = await connection.execute(
          'SELECT r.*, db.name as dorm_building_name FROM rooms r LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id WHERE r.id = ?',
          [bed2.room_id]
        );
        await connection.execute(
          'UPDATE users SET dorm_building_id = ?, room_number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [room2[0].dorm_building_id, room2[0].room_number, bed1.student_id]
        );
      }

      if (bed2.student_id) {
        const [room1] = await connection.execute(
          'SELECT r.*, db.name as dorm_building_name FROM rooms r LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id WHERE r.id = ?',
          [bed1.room_id]
        );
        await connection.execute(
          'UPDATE users SET dorm_building_id = ?, room_number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [room1[0].dorm_building_id, room1[0].room_number, bed2.student_id]
        );
      }
    });

    res.json({
      success: true,
      message: '床位调换成功'
    });
  } catch (error) {
    console.error('调换床位错误:', error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器内部错误'
    });
  }
}

module.exports = {
  getBeds,
  assignBed,
  releaseBed,
  swapBeds
};
