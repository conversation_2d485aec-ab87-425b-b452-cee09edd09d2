const { query } = require('../config/database');

/**
 * 获取水电费账单列表
 */
async function getUtilityBills(req, res) {
  try {
    const { studentId, month, isPaid } = req.query;
    const userRole = req.user.role;
    const userId = req.user.id;
    
    let sql = `
      SELECT ub.*, u.name as student_name, r.room_number, db.name as dorm_building_name
      FROM utility_bills ub
      LEFT JOIN users u ON ub.student_id = u.id
      LEFT JOIN rooms r ON ub.room_id = r.id
      LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
      WHERE 1=1
    `;
    const params = [];

    // 根据用户角色过滤数据
    if (userRole === '学生') {
      sql += ' AND ub.student_id = ?';
      params.push(userId);
    } else if (userRole === '宿舍管理员') {
      // 宿舍管理员只能看到自己管理的宿舍楼的账单
      const userInfo = await query(
        'SELECT dorm_building_id FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0) {
        sql += ' AND r.dorm_building_id = ?';
        params.push(userInfo[0].dorm_building_id);
      }
    }

    // 其他过滤条件
    if (studentId) {
      sql += ' AND ub.student_id = ?';
      params.push(studentId);
    }

    if (month) {
      sql += ' AND ub.month = ?';
      params.push(month);
    }

    if (isPaid !== undefined) {
      sql += ' AND ub.is_paid = ?';
      params.push(isPaid === 'true');
    }

    sql += ' ORDER BY ub.month DESC, ub.created_at DESC';

    const bills = await query(sql, params);

    res.json({
      success: true,
      data: {
        utilityBills: bills
      }
    });
  } catch (error) {
    console.error('获取水电费账单列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 创建水电费账单
 */
async function createUtilityBill(req, res) {
  try {
    const { 
      student_id, room_id, month, 
      electricity_usage, electricity_cost, 
      water_usage, water_cost 
    } = req.body;

    // 验证输入
    if (!student_id || !room_id || !month) {
      return res.status(400).json({
        success: false,
        message: '学生ID、房间ID和月份都是必填项'
      });
    }

    // 检查学生是否存在
    const students = await query(
      'SELECT id FROM users WHERE id = ? AND role = ?',
      [student_id, '学生']
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    // 检查房间是否存在
    const rooms = await query(
      'SELECT id FROM rooms WHERE id = ?',
      [room_id]
    );

    if (rooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    // 检查是否已存在该学生该月份的账单
    const existingBills = await query(
      'SELECT id FROM utility_bills WHERE student_id = ? AND month = ?',
      [student_id, month]
    );

    if (existingBills.length > 0) {
      return res.status(409).json({
        success: false,
        message: '该学生该月份的账单已存在'
      });
    }

    // 计算总费用
    const totalCost = (electricity_cost || 0) + (water_cost || 0);

    // 生成账单ID
    const billId = 'bill_' + Date.now();

    // 插入新账单
    await query(
      `INSERT INTO utility_bills (
        id, student_id, room_id, month, 
        electricity_usage, electricity_cost, 
        water_usage, water_cost, total_cost, is_paid
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        billId, student_id, room_id, month,
        electricity_usage || 0, electricity_cost || 0,
        water_usage || 0, water_cost || 0, totalCost, false
      ]
    );

    // 获取新创建的账单信息
    const newBills = await query(
      `SELECT ub.*, u.name as student_name, r.room_number, db.name as dorm_building_name
       FROM utility_bills ub
       LEFT JOIN users u ON ub.student_id = u.id
       LEFT JOIN rooms r ON ub.room_id = r.id
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE ub.id = ?`,
      [billId]
    );

    res.status(201).json({
      success: true,
      message: '水电费账单创建成功',
      data: {
        utilityBill: newBills[0]
      }
    });
  } catch (error) {
    console.error('创建水电费账单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 更新水电费账单
 */
async function updateUtilityBill(req, res) {
  try {
    const { id } = req.params;
    const { 
      electricity_usage, electricity_cost, 
      water_usage, water_cost, is_paid 
    } = req.body;

    // 检查账单是否存在
    const existingBills = await query(
      'SELECT * FROM utility_bills WHERE id = ?',
      [id]
    );

    if (existingBills.length === 0) {
      return res.status(404).json({
        success: false,
        message: '水电费账单不存在'
      });
    }

    // 计算新的总费用
    let totalCost = existingBills[0].total_cost;
    if (electricity_cost !== undefined || water_cost !== undefined) {
      const newElectricityCost = electricity_cost !== undefined ? electricity_cost : existingBills[0].electricity_cost;
      const newWaterCost = water_cost !== undefined ? water_cost : existingBills[0].water_cost;
      totalCost = newElectricityCost + newWaterCost;
    }

    // 更新账单信息
    await query(
      `UPDATE utility_bills SET 
        electricity_usage = COALESCE(?, electricity_usage),
        electricity_cost = COALESCE(?, electricity_cost),
        water_usage = COALESCE(?, water_usage),
        water_cost = COALESCE(?, water_cost),
        total_cost = ?,
        is_paid = COALESCE(?, is_paid),
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [
        electricity_usage, electricity_cost,
        water_usage, water_cost, totalCost,
        is_paid, id
      ]
    );

    // 获取更新后的账单信息
    const updatedBills = await query(
      `SELECT ub.*, u.name as student_name, r.room_number, db.name as dorm_building_name
       FROM utility_bills ub
       LEFT JOIN users u ON ub.student_id = u.id
       LEFT JOIN rooms r ON ub.room_id = r.id
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE ub.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '水电费账单更新成功',
      data: {
        utilityBill: updatedBills[0]
      }
    });
  } catch (error) {
    console.error('更新水电费账单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除水电费账单
 */
async function deleteUtilityBill(req, res) {
  try {
    const { id } = req.params;

    // 检查账单是否存在
    const existingBills = await query(
      'SELECT id FROM utility_bills WHERE id = ?',
      [id]
    );

    if (existingBills.length === 0) {
      return res.status(404).json({
        success: false,
        message: '水电费账单不存在'
      });
    }

    // 删除账单
    await query('DELETE FROM utility_bills WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '水电费账单删除成功'
    });
  } catch (error) {
    console.error('删除水电费账单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  getUtilityBills,
  createUtilityBill,
  updateUtilityBill,
  deleteUtilityBill
};
