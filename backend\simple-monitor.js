// 简单的数据库监控脚本
console.log('🔍 数据库监控启动...');

const mysql = require('mysql2');

// 创建数据库连接
const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'redhat'
});

// 连接数据库
connection.connect((err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err);
    return;
  }
  console.log('✅ 数据库连接成功');
  
  // 获取初始数据
  getTableCounts();
  
  // 每10秒检查一次
  setInterval(getTableCounts, 10000);
});

function getTableCounts() {
  const tables = ['colleges', 'majors', 'users', 'dorm_buildings', 'rooms', 'repair_requests'];
  
  console.log(`\n📊 ${new Date().toLocaleTimeString()} - 数据库状态:`);
  
  tables.forEach(table => {
    connection.query(`SELECT COUNT(*) as count FROM ${table}`, (err, results) => {
      if (err) {
        console.error(`❌ ${table}: 查询失败`);
      } else {
        console.log(`  ${table}: ${results[0].count} 条记录`);
      }
    });
  });
}

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 停止监控');
  connection.end();
  process.exit(0);
});
