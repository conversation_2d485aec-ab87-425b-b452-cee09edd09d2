// 前端、后端、数据库通信测试
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'root',
  database: 'redhat'
};

async function testCommunication() {
  console.log('🔍 测试前端、后端、数据库通信状态...\n');

  // 1. 测试数据库连接
  console.log('1. 测试数据库连接...');
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 测试查询
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    console.log(`✅ 数据库查询成功: 学院表有 ${rows[0].count} 条记录`);
    
    await connection.end();
  } catch (error) {
    console.log('❌ 数据库连接失败:', error.message);
    return;
  }

  // 2. 测试后端API
  console.log('\n2. 测试后端API...');
  try {
    // 测试健康检查
    const healthResponse = await fetch('http://localhost:3002/api/health');
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ 后端API健康检查成功:', healthData.message);
    } else {
      console.log('❌ 后端API健康检查失败:', healthResponse.status);
      return;
    }

    // 测试登录
    const loginResponse = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        role: '系统管理员',
        password: 'password123'
      })
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ 后端登录API成功:', loginData.data.user.name);
      
      const token = loginData.data.token;
      
      // 测试数据获取
      const collegesResponse = await fetch('http://localhost:3002/api/colleges', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (collegesResponse.ok) {
        const collegesData = await collegesResponse.json();
        console.log(`✅ 后端数据API成功: 获取到 ${collegesData.data.colleges.length} 个学院`);
      } else {
        console.log('❌ 后端数据API失败:', collegesResponse.status);
      }
    } else {
      console.log('❌ 后端登录API失败:', loginResponse.status);
    }
  } catch (error) {
    console.log('❌ 后端API测试失败:', error.message);
  }

  // 3. 测试前端访问
  console.log('\n3. 测试前端访问...');
  try {
    const frontendResponse = await fetch('http://localhost:5173');
    if (frontendResponse.ok) {
      console.log('✅ 前端服务访问成功');
    } else {
      console.log('❌ 前端服务访问失败:', frontendResponse.status);
    }
  } catch (error) {
    console.log('❌ 前端服务访问失败:', error.message);
  }

  // 4. 测试完整数据流
  console.log('\n4. 测试完整数据流 (前端 → 后端 → 数据库)...');
  try {
    // 登录获取token
    const loginResponse = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        role: '系统管理员',
        password: 'password123'
      })
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      const token = loginData.data.token;

      // 获取当前数据
      const beforeResponse = await fetch('http://localhost:3002/api/colleges', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const beforeData = await beforeResponse.json();
      const beforeCount = beforeData.data.colleges.length;

      // 添加新数据
      const addResponse = await fetch('http://localhost:3002/api/colleges', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: `通信测试学院_${Date.now()}`
        })
      });

      if (addResponse.ok) {
        const addData = await addResponse.json();
        console.log('✅ 数据添加成功:', addData.data.college.name);

        // 验证数据已保存
        const afterResponse = await fetch('http://localhost:3002/api/colleges', {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        const afterData = await afterResponse.json();
        const afterCount = afterData.data.colleges.length;

        if (afterCount === beforeCount + 1) {
          console.log(`✅ 数据流测试成功: 学院数量从 ${beforeCount} 增加到 ${afterCount}`);
          console.log('✅ 前端 → 后端 → 数据库 通信完全正常！');
        } else {
          console.log('❌ 数据流测试失败: 数据未正确保存');
        }
      } else {
        console.log('❌ 数据添加失败:', addResponse.status);
      }
    }
  } catch (error) {
    console.log('❌ 完整数据流测试失败:', error.message);
  }

  console.log('\n📊 通信测试完成！');
}

// 运行测试
testCommunication();
