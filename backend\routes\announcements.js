const express = require('express');
const router = express.Router();
const { getAnnouncements, createAnnouncement, updateAnnouncement, deleteAnnouncement } = require('../controllers/announcementController');
const { authenticateToken } = require('../middleware/auth');

// 获取公告列表
router.get('/', authenticateToken, getAnnouncements);

// 创建公告
router.post('/', authenticateToken, createAnnouncement);

// 更新公告
router.put('/:id', authenticateToken, updateAnnouncement);

// 删除公告
router.delete('/:id', authenticateToken, deleteAnnouncement);

module.exports = router;
