-- 快速初始化数据库脚本
USE redhat;

-- 删除现有表（如果存在）
DROP TABLE IF EXISTS civilized_dorm_scores;
DROP TABLE IF EXISTS violations;
DROP TABLE IF EXISTS visitors;
DROP TABLE IF EXISTS late_returns;
DROP TABLE IF EXISTS utility_bills;
DROP TABLE IF EXISTS announcements;
DROP TABLE IF EXISTS repair_updates;
DROP TABLE IF EXISTS repair_requests;
DROP TABLE IF EXISTS beds;
DROP TABLE IF EXISTS rooms;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS dorm_buildings;
DROP TABLE IF EXISTS majors;
DROP TABLE IF EXISTS colleges;

-- 1. 学院表
CREATE TABLE colleges (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 专业表
CREATE TABLE majors (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    college_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
    UNIQUE KEY unique_major_per_college (name, college_id)
);

-- 3. 宿舍楼表
CREATE TABLE dorm_buildings (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    floors INT NOT NULL DEFAULT 1,
    total_rooms INT NOT NULL DEFAULT 0,
    assigned_admin_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 用户表
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
    college VARCHAR(100),
    major VARCHAR(100),
    dorm_building_id VARCHAR(50),
    room_number VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON SET NULL
);

-- 5. 房间表
CREATE TABLE rooms (
    id VARCHAR(50) PRIMARY KEY,
    room_number VARCHAR(20) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    floor INT NOT NULL,
    type ENUM('单人间', '双人间', '四人间', '六人间') NOT NULL DEFAULT '四人间',
    capacity INT NOT NULL DEFAULT 4,
    occupied_beds INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_per_building (room_number, dorm_building_id)
);

-- 6. 床位表
CREATE TABLE beds (
    id VARCHAR(50) PRIMARY KEY,
    room_id VARCHAR(50) NOT NULL,
    bed_number VARCHAR(10) NOT NULL,
    status ENUM('空闲', '已入住') NOT NULL DEFAULT '空闲',
    student_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON SET NULL,
    UNIQUE KEY unique_bed_per_room (bed_number, room_id)
);

-- 7. 维修请求表
CREATE TABLE repair_requests (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    room_number VARCHAR(20) NOT NULL,
    dorm_building VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    image_url VARCHAR(500),
    contact VARCHAR(50) NOT NULL,
    status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') NOT NULL DEFAULT '待处理',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_to VARCHAR(50),
    assigned_to_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON SET NULL
);

-- 8. 维修更新记录表
CREATE TABLE repair_updates (
    id VARCHAR(50) PRIMARY KEY,
    repair_request_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) NOT NULL,
    notes TEXT NOT NULL,
    new_status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_request_id) REFERENCES repair_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 9. 公告表
CREATE TABLE announcements (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id VARCHAR(50) NOT NULL,
    author_name VARCHAR(100) NOT NULL,
    scope ENUM('All', 'College', 'DormBuilding') NOT NULL DEFAULT 'All',
    target_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 10. 水电费账单表
CREATE TABLE utility_bills (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    month VARCHAR(7) NOT NULL,
    electricity_usage DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    electricity_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    water_usage DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    water_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_paid BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    UNIQUE KEY unique_bill_per_student_month (student_id, month)
);

-- 11. 晚归记录表
CREATE TABLE late_returns (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    reason TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 12. 访客记录表
CREATE TABLE visitors (
    id VARCHAR(50) PRIMARY KEY,
    visitor_name VARCHAR(100) NOT NULL,
    visitor_id_number VARCHAR(50) NOT NULL,
    reason VARCHAR(200),
    entry_time TIMESTAMP NOT NULL,
    exit_time TIMESTAMP NULL,
    visited_student_id VARCHAR(50) NOT NULL,
    visited_student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (visited_student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 13. 违规记录表
CREATE TABLE violations (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 14. 文明宿舍评分表
CREATE TABLE civilized_dorm_scores (
    id VARCHAR(50) PRIMARY KEY,
    dorm_building_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    score INT NOT NULL CHECK (score >= 0 AND score <= 100),
    notes TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);
