import React, { useState, useEffect } from 'react';
import { User, UserRole } from '../../types';
import Table from '../../components/Table';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';
import Card from '../../components/Card';
import { API_BASE_URL } from '../../config/api';

const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<Partial<User>>({ role: UserRole.STUDENT });

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取用户列表失败');
      }

      const data = await response.json();
      if (data.success) {
        setUsers(data.data.users || []);
      }
    } catch (error) {
      console.error('获取用户列表错误:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    fetchUsers();
  }, []);

  const columns = [
    { header: '姓名', accessor: 'name' as keyof User},
    { header: '邮箱', accessor: 'email' as keyof User},
    { header: '角色', accessor: 'role' as keyof User},
    { header: '电话', accessor: 'phone' as keyof User, render: (user: User) => user.phone || '-' },
    { 
      header: '操作', 
      accessor: 'id' as keyof User, 
      render: (user: User) => (
        <div className="space-x-2">
          <Button size="sm" variant="ghost" onClick={() => handleEdit(user)}><i className="fas fa-edit"></i></Button>
          <Button size="sm" variant="danger" onClick={() => handleDelete(user.id)}><i className="fas fa-trash"></i></Button>
        </div>
      )
    }
  ];

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setNewUser(user); 
    setIsModalOpen(true);
  };

  const handleDelete = async (userId: string) => {
    if (window.confirm('您确定要删除此用户吗？')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('删除用户失败');
        }

        const data = await response.json();
        if (data.success) {
          // 重新获取用户列表
          await fetchUsers();
        }
      } catch (error) {
        console.error('删除用户错误:', error);
        alert('删除用户失败，请重试');
      }
    }
  };

  const handleOpenModal = (user: User | null = null) => {
    setEditingUser(user);
    setNewUser(user ? { ...user } : { role: UserRole.STUDENT });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingUser(null);
    setNewUser({ role: UserRole.STUDENT });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewUser(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('token');
      
      if (editingUser) {
        // 更新用户
        const response = await fetch(`${API_BASE_URL}/users/${editingUser.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: newUser.name,
            email: newUser.email,
            role: newUser.role,
            phone: newUser.phone,
            college: newUser.college,
            major: newUser.major,
            dorm_building_id: newUser.dormBuilding,
            room_number: newUser.roomNumber,
            emergency_contact_name: newUser.emergencyContactName,
            emergency_contact_phone: newUser.emergencyContactPhone
          }),
        });

        if (!response.ok) {
          throw new Error('更新用户失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchUsers();
          handleCloseModal();
        }
      } else {
        // 创建新用户
        const response = await fetch(`${API_BASE_URL}/users`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: newUser.name,
            email: newUser.email,
            password: 'password123', // 默认密码
            role: newUser.role,
            phone: newUser.phone,
            college: newUser.college,
            major: newUser.major,
            dorm_building_id: newUser.dormBuilding,
            room_number: newUser.roomNumber,
            emergency_contact_name: newUser.emergencyContactName,
            emergency_contact_phone: newUser.emergencyContactPhone
          }),
        });

        if (!response.ok) {
          throw new Error('创建用户失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchUsers();
          handleCloseModal();
        }
      }
    } catch (error) {
      console.error('保存用户错误:', error);
      alert('保存用户失败，请重试');
    }
  };

  if (isLoading) {
    return (
      <Card title="用户管理">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  return (
    <Card title="用户管理" actions={<Button onClick={() => handleOpenModal()} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加用户</Button>}>
      <Table columns={columns} data={users} keyExtractor={(user) => user.id} />

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingUser ? '编辑用户' : '添加新用户'}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="name" label="姓名" value={newUser.name || ''} onChange={handleInputChange} required />
          <Input name="email" label="邮箱" type="email" value={newUser.email || ''} onChange={handleInputChange} required />
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">角色</label>
            <select
              id="role"
              name="role"
              value={newUser.role}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              {Object.values(UserRole).map(role => (
                <option key={role} value={role}>{role}</option>
              ))}
            </select>
          </div>
          <Input name="phone" label="电话 (可选)" type="tel" value={newUser.phone || ''} onChange={handleInputChange} />
          { (newUser.role === UserRole.STUDENT || newUser.role === UserRole.DORM_ADMIN) && 
            <Input name="dormBuilding" label="宿舍楼 (可选)" value={newUser.dormBuilding || ''} onChange={handleInputChange} />
          }
          { newUser.role === UserRole.STUDENT && 
             <>
              <Input name="college" label="学院 (可选)" value={newUser.college || ''} onChange={handleInputChange} />
              <Input name="major" label="专业 (可选)" value={newUser.major || ''} onChange={handleInputChange} />
              <Input name="roomNumber" label="房间号 (可选)" value={newUser.roomNumber || ''} onChange={handleInputChange} />
              <Input name="emergencyContactName" label="第一紧急联系人姓名 (可选)" value={newUser.emergencyContactName || ''} onChange={handleInputChange} />
              <Input name="emergencyContactPhone" label="第一紧急联系人电话 (可选)" type="tel" value={newUser.emergencyContactPhone || ''} onChange={handleInputChange} />
             </>
          }
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">{editingUser ? '保存更改' : '添加用户'}</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default UserManagementPage;