const express = require('express');
const router = express.Router();
const { getRooms, createRoom, updateRoom, deleteRoom, getRoomBeds } = require('../controllers/roomController');
const { authenticateToken, requireDormAdmin } = require('../middleware/auth');

// 获取房间列表
router.get('/', authenticateToken, getRooms);

// 创建房间 (需要宿舍管理员或系统管理员权限)
router.post('/', authenticateToken, requireDormAdmin, createRoom);

// 更新房间 (需要宿舍管理员或系统管理员权限)
router.put('/:id', authenticateToken, requireDormAdmin, updateRoom);

// 删除房间 (需要宿舍管理员或系统管理员权限)
router.delete('/:id', authenticateToken, requireDormAdmin, deleteRoom);

// 获取房间的床位信息
router.get('/:id/beds', authenticateToken, getRoomBeds);

module.exports = router;
