const { query } = require('../config/database');

/**
 * 获取文明宿舍评分列表
 */
async function getCivilizedScores(req, res) {
  try {
    const { dormBuildingId, roomId, date } = req.query;
    const userRole = req.user.role;
    const userId = req.user.id;
    
    let sql = `
      SELECT cs.*, db.name as dorm_building_name, r.room_number, ru.name as recorded_by_name
      FROM civilized_dorm_scores cs
      LEFT JOIN dorm_buildings db ON cs.dorm_building_id = db.id
      LEFT JOIN rooms r ON cs.room_id = r.id
      LEFT JOIN users ru ON cs.recorded_by = ru.id
      WHERE 1=1
    `;
    const params = [];

    // 根据用户角色过滤数据
    if (userRole === '学生') {
      // 学生只能看到自己房间的评分
      const userInfo = await query(
        'SELECT dorm_building_id, room_number FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0 && userInfo[0].room_number) {
        const roomInfo = await query(
          'SELECT id FROM rooms WHERE room_number = ? AND dorm_building_id = ?',
          [userInfo[0].room_number, userInfo[0].dorm_building_id]
        );
        
        if (roomInfo.length > 0) {
          sql += ' AND cs.room_id = ?';
          params.push(roomInfo[0].id);
        }
      }
    } else if (userRole === '宿舍管理员') {
      // 宿舍管理员只能看到自己管理的宿舍楼的评分
      const userInfo = await query(
        'SELECT dorm_building_id FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0) {
        sql += ' AND cs.dorm_building_id = ?';
        params.push(userInfo[0].dorm_building_id);
      }
    }

    // 其他过滤条件
    if (dormBuildingId) {
      sql += ' AND cs.dorm_building_id = ?';
      params.push(dormBuildingId);
    }

    if (roomId) {
      sql += ' AND cs.room_id = ?';
      params.push(roomId);
    }

    if (date) {
      sql += ' AND cs.date = ?';
      params.push(date);
    }

    sql += ' ORDER BY cs.date DESC, cs.score DESC';

    const scores = await query(sql, params);

    res.json({
      success: true,
      data: {
        civilizedScores: scores
      }
    });
  } catch (error) {
    console.error('获取文明宿舍评分列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 创建文明宿舍评分
 */
async function createCivilizedScore(req, res) {
  try {
    const { dorm_building_id, room_id, date, score, notes } = req.body;
    const recordedBy = req.user.id;

    // 验证输入
    if (!dorm_building_id || !room_id || !date || score === undefined) {
      return res.status(400).json({
        success: false,
        message: '宿舍楼ID、房间ID、日期和评分都是必填项'
      });
    }

    // 验证评分范围
    if (score < 0 || score > 100) {
      return res.status(400).json({
        success: false,
        message: '评分必须在0-100之间'
      });
    }

    // 检查宿舍楼是否存在
    const buildings = await query(
      'SELECT id FROM dorm_buildings WHERE id = ?',
      [dorm_building_id]
    );

    if (buildings.length === 0) {
      return res.status(404).json({
        success: false,
        message: '宿舍楼不存在'
      });
    }

    // 检查房间是否存在
    const rooms = await query(
      'SELECT id FROM rooms WHERE id = ? AND dorm_building_id = ?',
      [room_id, dorm_building_id]
    );

    if (rooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在或不属于指定宿舍楼'
      });
    }

    // 检查是否已存在该房间该日期的评分
    const existingScores = await query(
      'SELECT id FROM civilized_dorm_scores WHERE room_id = ? AND date = ?',
      [room_id, date]
    );

    if (existingScores.length > 0) {
      return res.status(409).json({
        success: false,
        message: '该房间该日期的评分已存在'
      });
    }

    // 生成评分记录ID
    const scoreId = 'score_' + Date.now();

    // 插入新评分记录
    await query(
      `INSERT INTO civilized_dorm_scores (
        id, dorm_building_id, room_id, date, score, notes, recorded_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [scoreId, dorm_building_id, room_id, date, score, notes || null, recordedBy]
    );

    // 获取新创建的评分记录信息
    const newScores = await query(
      `SELECT cs.*, db.name as dorm_building_name, r.room_number, ru.name as recorded_by_name
       FROM civilized_dorm_scores cs
       LEFT JOIN dorm_buildings db ON cs.dorm_building_id = db.id
       LEFT JOIN rooms r ON cs.room_id = r.id
       LEFT JOIN users ru ON cs.recorded_by = ru.id
       WHERE cs.id = ?`,
      [scoreId]
    );

    res.status(201).json({
      success: true,
      message: '文明宿舍评分创建成功',
      data: {
        civilizedScore: newScores[0]
      }
    });
  } catch (error) {
    console.error('创建文明宿舍评分错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 更新文明宿舍评分
 */
async function updateCivilizedScore(req, res) {
  try {
    const { id } = req.params;
    const { score, notes } = req.body;
    const userRole = req.user.role;
    const userId = req.user.id;

    // 检查评分记录是否存在
    const existingScores = await query(
      'SELECT * FROM civilized_dorm_scores WHERE id = ?',
      [id]
    );

    if (existingScores.length === 0) {
      return res.status(404).json({
        success: false,
        message: '文明宿舍评分记录不存在'
      });
    }

    const scoreRecord = existingScores[0];

    // 权限检查：只有记录者或系统管理员可以修改
    if (scoreRecord.recorded_by !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能修改自己记录的评分'
      });
    }

    // 验证评分范围
    if (score !== undefined && (score < 0 || score > 100)) {
      return res.status(400).json({
        success: false,
        message: '评分必须在0-100之间'
      });
    }

    // 更新评分记录信息
    await query(
      `UPDATE civilized_dorm_scores SET 
        score = COALESCE(?, score),
        notes = ?,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [score, notes || null, id]
    );

    // 获取更新后的评分记录信息
    const updatedScores = await query(
      `SELECT cs.*, db.name as dorm_building_name, r.room_number, ru.name as recorded_by_name
       FROM civilized_dorm_scores cs
       LEFT JOIN dorm_buildings db ON cs.dorm_building_id = db.id
       LEFT JOIN rooms r ON cs.room_id = r.id
       LEFT JOIN users ru ON cs.recorded_by = ru.id
       WHERE cs.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '文明宿舍评分更新成功',
      data: {
        civilizedScore: updatedScores[0]
      }
    });
  } catch (error) {
    console.error('更新文明宿舍评分错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除文明宿舍评分
 */
async function deleteCivilizedScore(req, res) {
  try {
    const { id } = req.params;
    const userRole = req.user.role;
    const userId = req.user.id;

    // 检查评分记录是否存在
    const existingScores = await query(
      'SELECT * FROM civilized_dorm_scores WHERE id = ?',
      [id]
    );

    if (existingScores.length === 0) {
      return res.status(404).json({
        success: false,
        message: '文明宿舍评分记录不存在'
      });
    }

    const scoreRecord = existingScores[0];

    // 权限检查：只有记录者或系统管理员可以删除
    if (scoreRecord.recorded_by !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能删除自己记录的评分'
      });
    }

    // 删除评分记录
    await query('DELETE FROM civilized_dorm_scores WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '文明宿舍评分删除成功'
    });
  } catch (error) {
    console.error('删除文明宿舍评分错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  getCivilizedScores,
  createCivilizedScore,
  updateCivilizedScore,
  deleteCivilizedScore
};
