const { query } = require('../config/database');

/**
 * 获取房间列表
 */
async function getRooms(req, res) {
  try {
    const { dormBuildingId } = req.query;
    
    let sql = `
      SELECT r.*, db.name as dorm_building_name
      FROM rooms r
      LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
      WHERE 1=1
    `;
    const params = [];

    if (dormBuildingId) {
      sql += ' AND r.dorm_building_id = ?';
      params.push(dormBuildingId);
    }

    sql += ' ORDER BY r.dorm_building_id, r.floor, r.room_number';

    const rooms = await query(sql, params);

    res.json({
      success: true,
      data: {
        rooms: rooms
      }
    });
  } catch (error) {
    console.error('获取房间列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 创建房间
 */
async function createRoom(req, res) {
  try {
    const { room_number, dorm_building_id, floor, type, capacity } = req.body;

    // 验证输入
    if (!room_number || !dorm_building_id || !floor || !type || !capacity) {
      return res.status(400).json({
        success: false,
        message: '房间号、宿舍楼、楼层、类型和容量都是必填项'
      });
    }

    // 检查宿舍楼是否存在
    const buildings = await query(
      'SELECT id FROM dorm_buildings WHERE id = ?',
      [dorm_building_id]
    );

    if (buildings.length === 0) {
      return res.status(404).json({
        success: false,
        message: '指定的宿舍楼不存在'
      });
    }

    // 检查同一宿舍楼下房间号是否已存在
    const existingRooms = await query(
      'SELECT id FROM rooms WHERE room_number = ? AND dorm_building_id = ?',
      [room_number, dorm_building_id]
    );

    if (existingRooms.length > 0) {
      return res.status(409).json({
        success: false,
        message: '该宿舍楼下已存在同房间号'
      });
    }

    // 生成房间ID
    const roomId = 'room_' + Date.now();

    // 插入新房间
    await query(
      'INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [roomId, room_number, dorm_building_id, floor, type, capacity, 0]
    );

    // 创建床位
    const bedPromises = [];
    for (let i = 1; i <= capacity; i++) {
      const bedId = `bed_${roomId}_${i}`;
      bedPromises.push(
        query(
          'INSERT INTO beds (id, room_id, bed_number, status) VALUES (?, ?, ?, ?)',
          [bedId, roomId, i.toString(), '空闲']
        )
      );
    }
    await Promise.all(bedPromises);

    // 获取新创建的房间信息
    const newRooms = await query(
      `SELECT r.*, db.name as dorm_building_name
       FROM rooms r
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE r.id = ?`,
      [roomId]
    );

    res.status(201).json({
      success: true,
      message: '房间创建成功',
      data: {
        room: newRooms[0]
      }
    });
  } catch (error) {
    console.error('创建房间错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 更新房间
 */
async function updateRoom(req, res) {
  try {
    const { id } = req.params;
    const { room_number, floor, type, capacity } = req.body;

    // 检查房间是否存在
    const existingRooms = await query(
      'SELECT * FROM rooms WHERE id = ?',
      [id]
    );

    if (existingRooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    const room = existingRooms[0];

    // 如果更改了容量，需要检查是否有足够的空床位
    if (capacity && capacity < room.capacity) {
      const occupiedBeds = await query(
        'SELECT COUNT(*) as count FROM beds WHERE room_id = ? AND status = ?',
        [id, '已入住']
      );

      if (occupiedBeds[0].count > capacity) {
        return res.status(409).json({
          success: false,
          message: `无法减少容量，当前有${occupiedBeds[0].count}个床位已被占用`
        });
      }
    }

    // 更新房间信息
    await query(
      `UPDATE rooms SET 
        room_number = COALESCE(?, room_number),
        floor = COALESCE(?, floor),
        type = COALESCE(?, type),
        capacity = COALESCE(?, capacity),
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [room_number, floor, type, capacity, id]
    );

    // 如果容量发生变化，调整床位
    if (capacity && capacity !== room.capacity) {
      if (capacity > room.capacity) {
        // 增加床位
        const bedPromises = [];
        for (let i = room.capacity + 1; i <= capacity; i++) {
          const bedId = `bed_${id}_${i}`;
          bedPromises.push(
            query(
              'INSERT INTO beds (id, room_id, bed_number, status) VALUES (?, ?, ?, ?)',
              [bedId, id, i.toString(), '空闲']
            )
          );
        }
        await Promise.all(bedPromises);
      } else {
        // 减少床位（删除多余的空床位）
        await query(
          'DELETE FROM beds WHERE room_id = ? AND bed_number > ? AND status = ?',
          [id, capacity.toString(), '空闲']
        );
      }
    }

    // 获取更新后的房间信息
    const updatedRooms = await query(
      `SELECT r.*, db.name as dorm_building_name
       FROM rooms r
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE r.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '房间更新成功',
      data: {
        room: updatedRooms[0]
      }
    });
  } catch (error) {
    console.error('更新房间错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除房间
 */
async function deleteRoom(req, res) {
  try {
    const { id } = req.params;

    // 检查房间是否存在
    const existingRooms = await query(
      'SELECT id FROM rooms WHERE id = ?',
      [id]
    );

    if (existingRooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    // 检查是否有学生入住
    const occupiedBeds = await query(
      'SELECT COUNT(*) as count FROM beds WHERE room_id = ? AND status = ?',
      [id, '已入住']
    );

    if (occupiedBeds[0].count > 0) {
      return res.status(409).json({
        success: false,
        message: '无法删除房间，还有学生入住'
      });
    }

    // 删除房间（床位会因为外键约束自动删除）
    await query('DELETE FROM rooms WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '房间删除成功'
    });
  } catch (error) {
    console.error('删除房间错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 获取房间的床位信息
 */
async function getRoomBeds(req, res) {
  try {
    const { id } = req.params;

    // 检查房间是否存在
    const existingRooms = await query(
      'SELECT id FROM rooms WHERE id = ?',
      [id]
    );

    if (existingRooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    // 获取床位信息
    const beds = await query(
      `SELECT b.*, u.name as student_name
       FROM beds b
       LEFT JOIN users u ON b.student_id = u.id
       WHERE b.room_id = ?
       ORDER BY CAST(b.bed_number AS UNSIGNED)`,
      [id]
    );

    res.json({
      success: true,
      data: {
        beds: beds
      }
    });
  } catch (error) {
    console.error('获取房间床位错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  getRooms,
  createRoom,
  updateRoom,
  deleteRoom,
  getRoomBeds
};
