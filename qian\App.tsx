import React from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import MainLayout from './layouts/MainLayout';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import UserManagementPage from './pages/admin/UserManagementPage';
import DormBuildingManagementPage from './pages/admin/DormBuildingManagementPage';
import CollegeManagementPage from './pages/admin/CollegeManagementPage';
import MajorManagementPage from './pages/admin/MajorManagementPage';
import RepairPage from './pages/RepairPage';
import AnnouncementPage from './pages/AnnouncementPage';
import { UserRole } from './types';

// Dorm Admin Pages
import RoomManagementPage from './pages/dorm-admin/RoomManagementPage';
import StudentAllocationPage from './pages/dorm-admin/StudentAllocationPage';
import ViolationRecordPage from './pages/dorm-admin/ViolationRecordPage';
import LateReturnRecordPage from './pages/dorm-admin/LateReturnRecordPage';
import VisitorRecordPage from './pages/dorm-admin/VisitorRecordPage';
import CivilizedDormPage from './pages/dorm-admin/CivilizedDormPage';

// Student Pages
import MyInfoPage from './pages/student/MyInfoPage';
import UtilityBillsPage from './pages/student/UtilityBillsPage';

// Repair Staff Pages
import MyTasksPage from './pages/repair-staff/MyTasksPage';

interface ProtectedRouteProps {
  children: JSX.Element;
  allowedRoles?: UserRole[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, allowedRoles }) => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles && !allowedRoles.includes(currentUser.role)) {
    return <Navigate to="/dashboard" replace />; 
  }

  return children;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route 
        path="/*"
        element={
          <ProtectedRoute>
            <MainLayout>
              <Routes>
                <Route path="dashboard" element={<DashboardPage />} />
                
                {/* Admin Routes */}
                <Route path="admin/users" element={<ProtectedRoute allowedRoles={[UserRole.SYSTEM_ADMIN]}><UserManagementPage /></ProtectedRoute>} />
                <Route path="admin/dorm-buildings" element={<ProtectedRoute allowedRoles={[UserRole.SYSTEM_ADMIN]}><DormBuildingManagementPage /></ProtectedRoute>} />
                <Route path="admin/colleges" element={<ProtectedRoute allowedRoles={[UserRole.SYSTEM_ADMIN]}><CollegeManagementPage /></ProtectedRoute>} /> 
                <Route path="admin/majors" element={<ProtectedRoute allowedRoles={[UserRole.SYSTEM_ADMIN]}><MajorManagementPage /></ProtectedRoute>} />
                
                {/* Dorm Admin Routes */}
                <Route path="dorm-admin/rooms" element={<ProtectedRoute allowedRoles={[UserRole.DORM_ADMIN]}><RoomManagementPage /></ProtectedRoute>} />
                <Route path="dorm-admin/allocation" element={<ProtectedRoute allowedRoles={[UserRole.DORM_ADMIN]}><StudentAllocationPage /></ProtectedRoute>} />
                <Route path="dorm-admin/violations" element={<ProtectedRoute allowedRoles={[UserRole.DORM_ADMIN]}><ViolationRecordPage /></ProtectedRoute>} />
                <Route path="dorm-admin/late-returns" element={<ProtectedRoute allowedRoles={[UserRole.DORM_ADMIN]}><LateReturnRecordPage /></ProtectedRoute>} />
                <Route path="dorm-admin/visitors" element={<ProtectedRoute allowedRoles={[UserRole.DORM_ADMIN]}><VisitorRecordPage /></ProtectedRoute>} />
                <Route path="dorm-admin/civilized-dorm" element={<ProtectedRoute allowedRoles={[UserRole.DORM_ADMIN]}><CivilizedDormPage /></ProtectedRoute>} />

                {/* Student Routes */}
                <Route path="student/my-info" element={<ProtectedRoute allowedRoles={[UserRole.STUDENT]}><MyInfoPage /></ProtectedRoute>} />
                <Route path="student/utility-bills" element={<ProtectedRoute allowedRoles={[UserRole.STUDENT]}><UtilityBillsPage /></ProtectedRoute>} />

                {/* Repair Staff Routes */}
                <Route path="repair/my-tasks" element={<ProtectedRoute allowedRoles={[UserRole.REPAIR_STAFF]}><MyTasksPage /></ProtectedRoute>} />

                {/* Shared Routes */}
                <Route path="repairs" element={<RepairPage />} />
                <Route path="announcements" element={<AnnouncementPage />} />
                
                {/* Fallback for unmatched routes within MainLayout */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </MainLayout>
          </ProtectedRoute>
        } 
      />
      <Route path="*" element={<Navigate to="/login" replace />} />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <HashRouter>
      <AuthProvider>
        <AppRoutes />
      </AuthProvider>
    </HashRouter>
  );
};

export default App; 