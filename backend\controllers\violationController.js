const { query } = require('../config/database');

/**
 * 获取违规记录列表
 */
async function getViolations(req, res) {
  try {
    const { studentId, dormBuildingId, type, date } = req.query;
    const userRole = req.user.role;
    const userId = req.user.id;
    
    let sql = `
      SELECT v.*, u.name as student_name, db.name as dorm_building_name, ru.name as recorded_by_name
      FROM violations v
      LEFT JOIN users u ON v.student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
      LEFT JOIN users ru ON v.recorded_by = ru.id
      WHERE 1=1
    `;
    const params = [];

    // 根据用户角色过滤数据
    if (userRole === '学生') {
      sql += ' AND v.student_id = ?';
      params.push(userId);
    } else if (userRole === '宿舍管理员') {
      // 宿舍管理员只能看到自己管理的宿舍楼的违规记录
      const userInfo = await query(
        'SELECT dorm_building_id FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0) {
        sql += ' AND v.dorm_building_id = ?';
        params.push(userInfo[0].dorm_building_id);
      }
    }

    // 其他过滤条件
    if (studentId) {
      sql += ' AND v.student_id = ?';
      params.push(studentId);
    }

    if (dormBuildingId) {
      sql += ' AND v.dorm_building_id = ?';
      params.push(dormBuildingId);
    }

    if (type) {
      sql += ' AND v.type = ?';
      params.push(type);
    }

    if (date) {
      sql += ' AND v.date = ?';
      params.push(date);
    }

    sql += ' ORDER BY v.date DESC, v.created_at DESC';

    const violations = await query(sql, params);

    res.json({
      success: true,
      data: {
        violations: violations
      }
    });
  } catch (error) {
    console.error('获取违规记录列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 创建违规记录
 */
async function createViolation(req, res) {
  try {
    const { student_id, dorm_building_id, date, type, description, action_taken } = req.body;
    const recordedBy = req.user.id;

    // 验证输入
    if (!student_id || !dorm_building_id || !date || !type || !description) {
      return res.status(400).json({
        success: false,
        message: '学生ID、宿舍楼ID、日期、违规类型和描述都是必填项'
      });
    }

    // 检查学生是否存在
    const students = await query(
      'SELECT id, name FROM users WHERE id = ? AND role = ?',
      [student_id, '学生']
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    // 检查宿舍楼是否存在
    const buildings = await query(
      'SELECT id FROM dorm_buildings WHERE id = ?',
      [dorm_building_id]
    );

    if (buildings.length === 0) {
      return res.status(404).json({
        success: false,
        message: '宿舍楼不存在'
      });
    }

    // 生成违规记录ID
    const violationId = 'vio_' + Date.now();

    // 插入新违规记录
    await query(
      `INSERT INTO violations (
        id, student_id, student_name, dorm_building_id, 
        date, type, description, action_taken, recorded_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        violationId, student_id, students[0].name, dorm_building_id,
        date, type, description, action_taken || null, recordedBy
      ]
    );

    // 获取新创建的违规记录信息
    const newViolations = await query(
      `SELECT v.*, u.name as student_name, db.name as dorm_building_name, ru.name as recorded_by_name
       FROM violations v
       LEFT JOIN users u ON v.student_id = u.id
       LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
       LEFT JOIN users ru ON v.recorded_by = ru.id
       WHERE v.id = ?`,
      [violationId]
    );

    res.status(201).json({
      success: true,
      message: '违规记录创建成功',
      data: {
        violation: newViolations[0]
      }
    });
  } catch (error) {
    console.error('创建违规记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 更新违规记录
 */
async function updateViolation(req, res) {
  try {
    const { id } = req.params;
    const { type, description, action_taken } = req.body;
    const userRole = req.user.role;
    const userId = req.user.id;

    // 检查违规记录是否存在
    const existingViolations = await query(
      'SELECT * FROM violations WHERE id = ?',
      [id]
    );

    if (existingViolations.length === 0) {
      return res.status(404).json({
        success: false,
        message: '违规记录不存在'
      });
    }

    const violation = existingViolations[0];

    // 权限检查：只有记录者或系统管理员可以修改
    if (violation.recorded_by !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能修改自己记录的违规记录'
      });
    }

    // 更新违规记录信息
    await query(
      `UPDATE violations SET 
        type = COALESCE(?, type),
        description = COALESCE(?, description),
        action_taken = ?,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [type, description, action_taken || null, id]
    );

    // 获取更新后的违规记录信息
    const updatedViolations = await query(
      `SELECT v.*, u.name as student_name, db.name as dorm_building_name, ru.name as recorded_by_name
       FROM violations v
       LEFT JOIN users u ON v.student_id = u.id
       LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
       LEFT JOIN users ru ON v.recorded_by = ru.id
       WHERE v.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '违规记录更新成功',
      data: {
        violation: updatedViolations[0]
      }
    });
  } catch (error) {
    console.error('更新违规记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除违规记录
 */
async function deleteViolation(req, res) {
  try {
    const { id } = req.params;
    const userRole = req.user.role;
    const userId = req.user.id;

    // 检查违规记录是否存在
    const existingViolations = await query(
      'SELECT * FROM violations WHERE id = ?',
      [id]
    );

    if (existingViolations.length === 0) {
      return res.status(404).json({
        success: false,
        message: '违规记录不存在'
      });
    }

    const violation = existingViolations[0];

    // 权限检查：只有记录者或系统管理员可以删除
    if (violation.recorded_by !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能删除自己记录的违规记录'
      });
    }

    // 删除违规记录
    await query('DELETE FROM violations WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '违规记录删除成功'
    });
  } catch (error) {
    console.error('删除违规记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  getViolations,
  createViolation,
  updateViolation,
  deleteViolation
};
