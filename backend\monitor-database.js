// 数据库实时监控脚本
const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'redhat',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 执行查询的辅助函数
async function query(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
}

async function monitorDatabase() {
  console.log('🔍 开始监控数据库变化...\n');
  
  let previousCounts = {};
  
  const tables = [
    'colleges',
    'majors', 
    'dorm_buildings',
    'users',
    'rooms',
    'beds',
    'repair_requests',
    'announcements',
    'utility_bills',
    'violations',
    'visitors',
    'late_returns',
    'civilized_dorm_scores'
  ];

  // 获取初始计数
  for (const table of tables) {
    try {
      const result = await query(`SELECT COUNT(*) as count FROM ${table}`);
      previousCounts[table] = result[0].count;
    } catch (error) {
      console.error(`❌ 获取${table}表计数失败:`, error.message);
      previousCounts[table] = 0;
    }
  }

  console.log('📊 初始数据统计:');
  for (const [table, count] of Object.entries(previousCounts)) {
    console.log(`  ${table}: ${count} 条记录`);
  }
  console.log('\n⏰ 开始监控变化...\n');

  // 每5秒检查一次变化
  setInterval(async () => {
    let hasChanges = false;
    
    for (const table of tables) {
      try {
        const result = await query(`SELECT COUNT(*) as count FROM ${table}`);
        const currentCount = result[0].count;
        
        if (currentCount !== previousCounts[table]) {
          const change = currentCount - previousCounts[table];
          const changeType = change > 0 ? '增加' : '减少';
          const changeIcon = change > 0 ? '📈' : '📉';
          
          console.log(`${changeIcon} ${new Date().toLocaleTimeString()} - ${table}表 ${changeType} ${Math.abs(change)} 条记录 (${previousCounts[table]} → ${currentCount})`);
          
          // 如果是新增记录，显示最新的记录
          if (change > 0) {
            try {
              const latestRecords = await query(`SELECT * FROM ${table} ORDER BY created_at DESC LIMIT ${change}`);
              console.log(`   📝 新增记录:`);
              latestRecords.forEach((record, index) => {
                const displayRecord = { ...record };
                // 隐藏密码字段
                if (displayRecord.password) delete displayRecord.password;
                console.log(`     ${index + 1}. ${JSON.stringify(displayRecord, null, 2).replace(/\n/g, '\n       ')}`);
              });
            } catch (error) {
              console.log(`   ⚠️  无法获取新增记录详情: ${error.message}`);
            }
          }
          
          previousCounts[table] = currentCount;
          hasChanges = true;
        }
      } catch (error) {
        console.error(`❌ 检查${table}表时出错:`, error.message);
      }
    }
    
    if (!hasChanges) {
      // 每分钟显示一次"无变化"状态
      const now = new Date();
      if (now.getSeconds() === 0) {
        console.log(`⏱️  ${now.toLocaleTimeString()} - 数据库无变化`);
      }
    }
  }, 5000); // 每5秒检查一次
}

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n\n🛑 停止监控数据库');
  process.exit(0);
});

// 启动监控
monitorDatabase().catch(error => {
  console.error('❌ 监控启动失败:', error);
  process.exit(1);
});
