const { query } = require('../config/database');

/**
 * 获取公告列表
 */
async function getAnnouncements(req, res) {
  try {
    const { scope, targetId } = req.query;
    const userRole = req.user.role;
    const userId = req.user.id;
    
    let sql = `
      SELECT a.*, u.name as author_name
      FROM announcements a
      LEFT JOIN users u ON a.author_id = u.id
      WHERE 1=1
    `;
    const params = [];

    // 根据用户角色和权限过滤公告
    if (userRole === '学生') {
      // 学生只能看到全体公告、自己学院的公告、自己宿舍楼的公告
      const userInfo = await query(
        'SELECT college, dorm_building_id FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0) {
        const user = userInfo[0];
        sql += ` AND (
          a.scope = 'All' 
          OR (a.scope = 'College' AND a.target_id = ?)
          OR (a.scope = 'DormBuilding' AND a.target_id = ?)
        )`;
        params.push(user.college, user.dorm_building_id);
      }
    } else if (userRole === '宿舍管理员') {
      // 宿舍管理员可以看到全体公告和自己管理的宿舍楼公告
      const userInfo = await query(
        'SELECT dorm_building_id FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0) {
        const user = userInfo[0];
        sql += ` AND (
          a.scope = 'All' 
          OR (a.scope = 'DormBuilding' AND a.target_id = ?)
          OR a.author_id = ?
        )`;
        params.push(user.dorm_building_id, userId);
      }
    }
    // 系统管理员和维修人员可以看到所有公告

    // 其他过滤条件
    if (scope) {
      sql += ' AND a.scope = ?';
      params.push(scope);
    }

    if (targetId) {
      sql += ' AND a.target_id = ?';
      params.push(targetId);
    }

    sql += ' ORDER BY a.created_at DESC';

    const announcements = await query(sql, params);

    res.json({
      success: true,
      data: {
        announcements: announcements
      }
    });
  } catch (error) {
    console.error('获取公告列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 创建公告
 */
async function createAnnouncement(req, res) {
  try {
    const { title, content, scope, target_id } = req.body;
    const authorId = req.user.id;
    const authorName = req.user.name;
    const userRole = req.user.role;

    // 验证输入
    if (!title || !content || !scope) {
      return res.status(400).json({
        success: false,
        message: '标题、内容和范围都是必填项'
      });
    }

    // 权限检查
    if (userRole === '学生' || userRole === '维修人员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，无法发布公告'
      });
    }

    // 宿舍管理员只能发布自己管理的宿舍楼公告
    if (userRole === '宿舍管理员') {
      if (scope !== 'DormBuilding') {
        return res.status(403).json({
          success: false,
          message: '宿舍管理员只能发布宿舍楼公告'
        });
      }

      const userInfo = await query(
        'SELECT dorm_building_id FROM users WHERE id = ?',
        [authorId]
      );

      if (userInfo.length === 0 || userInfo[0].dorm_building_id !== target_id) {
        return res.status(403).json({
          success: false,
          message: '只能发布自己管理的宿舍楼公告'
        });
      }
    }

    // 验证目标ID
    if (scope !== 'All' && !target_id) {
      return res.status(400).json({
        success: false,
        message: '指定范围时必须提供目标ID'
      });
    }

    if (scope === 'College') {
      const colleges = await query(
        'SELECT id FROM colleges WHERE id = ?',
        [target_id]
      );
      if (colleges.length === 0) {
        return res.status(404).json({
          success: false,
          message: '指定的学院不存在'
        });
      }
    } else if (scope === 'DormBuilding') {
      const buildings = await query(
        'SELECT id FROM dorm_buildings WHERE id = ?',
        [target_id]
      );
      if (buildings.length === 0) {
        return res.status(404).json({
          success: false,
          message: '指定的宿舍楼不存在'
        });
      }
    }

    // 生成公告ID
    const announcementId = 'ann_' + Date.now();

    // 插入新公告
    await query(
      'INSERT INTO announcements (id, title, content, author_id, author_name, scope, target_id) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [announcementId, title, content, authorId, authorName, scope, target_id || null]
    );

    // 获取新创建的公告信息
    const newAnnouncements = await query(
      'SELECT * FROM announcements WHERE id = ?',
      [announcementId]
    );

    res.status(201).json({
      success: true,
      message: '公告发布成功',
      data: {
        announcement: newAnnouncements[0]
      }
    });
  } catch (error) {
    console.error('创建公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 更新公告
 */
async function updateAnnouncement(req, res) {
  try {
    const { id } = req.params;
    const { title, content, scope, target_id } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 检查公告是否存在
    const existingAnnouncements = await query(
      'SELECT * FROM announcements WHERE id = ?',
      [id]
    );

    if (existingAnnouncements.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    const announcement = existingAnnouncements[0];

    // 权限检查：只有作者或系统管理员可以修改
    if (announcement.author_id !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能修改自己发布的公告'
      });
    }

    // 更新公告信息
    await query(
      `UPDATE announcements SET 
        title = COALESCE(?, title),
        content = COALESCE(?, content),
        scope = COALESCE(?, scope),
        target_id = ?,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [title, content, scope, target_id || null, id]
    );

    // 获取更新后的公告信息
    const updatedAnnouncements = await query(
      'SELECT * FROM announcements WHERE id = ?',
      [id]
    );

    res.json({
      success: true,
      message: '公告更新成功',
      data: {
        announcement: updatedAnnouncements[0]
      }
    });
  } catch (error) {
    console.error('更新公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除公告
 */
async function deleteAnnouncement(req, res) {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 检查公告是否存在
    const existingAnnouncements = await query(
      'SELECT * FROM announcements WHERE id = ?',
      [id]
    );

    if (existingAnnouncements.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    const announcement = existingAnnouncements[0];

    // 权限检查：只有作者或系统管理员可以删除
    if (announcement.author_id !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能删除自己发布的公告'
      });
    }

    // 删除公告
    await query('DELETE FROM announcements WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '公告删除成功'
    });
  } catch (error) {
    console.error('删除公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  getAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement
};
