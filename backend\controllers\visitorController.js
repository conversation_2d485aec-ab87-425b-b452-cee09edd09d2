const { query } = require('../config/database');

/**
 * 获取访客记录列表
 */
async function getVisitors(req, res) {
  try {
    const { visitedStudentId, dormBuildingId, date } = req.query;
    const userRole = req.user.role;
    const userId = req.user.id;
    
    let sql = `
      SELECT v.*, u.name as visited_student_name, db.name as dorm_building_name, ru.name as recorded_by_name
      FROM visitors v
      LEFT JOIN users u ON v.visited_student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
      LEFT JOIN users ru ON v.recorded_by = ru.id
      WHERE 1=1
    `;
    const params = [];

    // 根据用户角色过滤数据
    if (userRole === '学生') {
      sql += ' AND v.visited_student_id = ?';
      params.push(userId);
    } else if (userRole === '宿舍管理员') {
      // 宿舍管理员只能看到自己管理的宿舍楼的访客记录
      const userInfo = await query(
        'SELECT dorm_building_id FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0) {
        sql += ' AND v.dorm_building_id = ?';
        params.push(userInfo[0].dorm_building_id);
      }
    }

    // 其他过滤条件
    if (visitedStudentId) {
      sql += ' AND v.visited_student_id = ?';
      params.push(visitedStudentId);
    }

    if (dormBuildingId) {
      sql += ' AND v.dorm_building_id = ?';
      params.push(dormBuildingId);
    }

    if (date) {
      sql += ' AND DATE(v.entry_time) = ?';
      params.push(date);
    }

    sql += ' ORDER BY v.entry_time DESC';

    const visitors = await query(sql, params);

    res.json({
      success: true,
      data: {
        visitors: visitors
      }
    });
  } catch (error) {
    console.error('获取访客记录列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 创建访客记录
 */
async function createVisitor(req, res) {
  try {
    const { 
      visitor_name, visitor_id_number, reason, entry_time,
      visited_student_id, dorm_building_id 
    } = req.body;
    const recordedBy = req.user.id;

    // 验证输入
    if (!visitor_name || !visitor_id_number || !entry_time || !visited_student_id || !dorm_building_id) {
      return res.status(400).json({
        success: false,
        message: '访客姓名、身份证号、入住时间、被访学生ID和宿舍楼ID都是必填项'
      });
    }

    // 检查被访学生是否存在
    const students = await query(
      'SELECT id, name FROM users WHERE id = ? AND role = ?',
      [visited_student_id, '学生']
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '被访学生不存在'
      });
    }

    // 检查宿舍楼是否存在
    const buildings = await query(
      'SELECT id FROM dorm_buildings WHERE id = ?',
      [dorm_building_id]
    );

    if (buildings.length === 0) {
      return res.status(404).json({
        success: false,
        message: '宿舍楼不存在'
      });
    }

    // 生成访客记录ID
    const visitorId = 'vis_' + Date.now();

    // 插入新访客记录
    await query(
      `INSERT INTO visitors (
        id, visitor_name, visitor_id_number, reason, entry_time,
        visited_student_id, visited_student_name, dorm_building_id, recorded_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        visitorId, visitor_name, visitor_id_number, reason || null, entry_time,
        visited_student_id, students[0].name, dorm_building_id, recordedBy
      ]
    );

    // 获取新创建的访客记录信息
    const newVisitors = await query(
      `SELECT v.*, u.name as visited_student_name, db.name as dorm_building_name, ru.name as recorded_by_name
       FROM visitors v
       LEFT JOIN users u ON v.visited_student_id = u.id
       LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
       LEFT JOIN users ru ON v.recorded_by = ru.id
       WHERE v.id = ?`,
      [visitorId]
    );

    res.status(201).json({
      success: true,
      message: '访客记录创建成功',
      data: {
        visitor: newVisitors[0]
      }
    });
  } catch (error) {
    console.error('创建访客记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 更新访客记录（主要用于登记离开时间）
 */
async function updateVisitor(req, res) {
  try {
    const { id } = req.params;
    const { exit_time, reason } = req.body;

    // 检查访客记录是否存在
    const existingVisitors = await query(
      'SELECT * FROM visitors WHERE id = ?',
      [id]
    );

    if (existingVisitors.length === 0) {
      return res.status(404).json({
        success: false,
        message: '访客记录不存在'
      });
    }

    // 更新访客记录信息
    await query(
      `UPDATE visitors SET 
        exit_time = COALESCE(?, exit_time),
        reason = COALESCE(?, reason),
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [exit_time, reason, id]
    );

    // 获取更新后的访客记录信息
    const updatedVisitors = await query(
      `SELECT v.*, u.name as visited_student_name, db.name as dorm_building_name, ru.name as recorded_by_name
       FROM visitors v
       LEFT JOIN users u ON v.visited_student_id = u.id
       LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
       LEFT JOIN users ru ON v.recorded_by = ru.id
       WHERE v.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '访客记录更新成功',
      data: {
        visitor: updatedVisitors[0]
      }
    });
  } catch (error) {
    console.error('更新访客记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除访客记录
 */
async function deleteVisitor(req, res) {
  try {
    const { id } = req.params;
    const userRole = req.user.role;
    const userId = req.user.id;

    // 检查访客记录是否存在
    const existingVisitors = await query(
      'SELECT * FROM visitors WHERE id = ?',
      [id]
    );

    if (existingVisitors.length === 0) {
      return res.status(404).json({
        success: false,
        message: '访客记录不存在'
      });
    }

    const visitor = existingVisitors[0];

    // 权限检查：只有记录者或系统管理员可以删除
    if (visitor.recorded_by !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能删除自己记录的访客记录'
      });
    }

    // 删除访客记录
    await query('DELETE FROM visitors WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '访客记录删除成功'
    });
  } catch (error) {
    console.error('删除访客记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  getVisitors,
  createVisitor,
  updateVisitor,
  deleteVisitor
};
