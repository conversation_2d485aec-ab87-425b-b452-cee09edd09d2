// 测试前端后端数据同步
const API_BASE_URL = 'http://localhost:3002/api';

async function testFrontendBackendSync() {
  console.log('🧪 测试前端后端数据同步...\n');

  try {
    // 1. 登录获取token
    console.log('1. 登录系统管理员...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        role: '系统管理员',
        password: 'password123'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`登录失败: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    console.log('✅ 登录成功:', loginData.data.user.name);
    
    const token = loginData.data.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 获取当前学院数量
    console.log('\n2. 获取当前学院数量...');
    const collegesResponse = await fetch(`${API_BASE_URL}/colleges`, { headers });
    
    if (!collegesResponse.ok) {
      throw new Error(`获取学院列表失败: ${collegesResponse.status}`);
    }
    
    const collegesData = await collegesResponse.json();
    const initialCount = collegesData.data.colleges.length;
    console.log(`✅ 当前学院数量: ${initialCount}`);
    
    // 显示现有学院
    console.log('📋 现有学院:');
    collegesData.data.colleges.forEach((college, index) => {
      console.log(`  ${index + 1}. ${college.name} (ID: ${college.id})`);
    });

    // 3. 添加新学院
    console.log('\n3. 添加新学院...');
    const newCollegeName = `API测试学院_${new Date().getTime()}`;
    const addResponse = await fetch(`${API_BASE_URL}/colleges`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ name: newCollegeName })
    });

    if (!addResponse.ok) {
      const errorData = await addResponse.json();
      throw new Error(`添加学院失败: ${errorData.message}`);
    }

    const addData = await addResponse.json();
    console.log('✅ 学院添加成功:', addData.data.college.name);
    console.log('📝 新学院ID:', addData.data.college.id);

    // 4. 验证学院已添加
    console.log('\n4. 验证学院已添加...');
    const verifyResponse = await fetch(`${API_BASE_URL}/colleges`, { headers });
    const verifyData = await verifyResponse.json();
    const newCount = verifyData.data.colleges.length;
    
    console.log(`✅ 验证成功: 学院数量从 ${initialCount} 增加到 ${newCount}`);
    
    if (newCount === initialCount + 1) {
      console.log('🎉 数据同步正常！前端添加的数据已成功保存到数据库');
    } else {
      console.log('❌ 数据同步异常！');
    }

    // 5. 测试其他功能
    console.log('\n5. 测试其他CRUD功能...');
    
    // 测试添加专业
    const majorResponse = await fetch(`${API_BASE_URL}/majors`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: `测试专业_${new Date().getTime()}`,
        college_id: addData.data.college.id
      })
    });

    if (majorResponse.ok) {
      const majorData = await majorResponse.json();
      console.log('✅ 专业添加成功:', majorData.data.major.name);
    } else {
      console.log('❌ 专业添加失败');
    }

    // 测试添加宿舍楼
    const buildingResponse = await fetch(`${API_BASE_URL}/dorm-buildings`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: `测试宿舍楼_${new Date().getTime()}`,
        floors: 5,
        total_rooms: 100
      })
    });

    if (buildingResponse.ok) {
      const buildingData = await buildingResponse.json();
      console.log('✅ 宿舍楼添加成功:', buildingData.data.dormBuilding.name);
    } else {
      console.log('❌ 宿舍楼添加失败');
    }

    // 6. 最终验证
    console.log('\n6. 最终数据统计...');
    const finalStats = await Promise.all([
      fetch(`${API_BASE_URL}/colleges`, { headers }).then(r => r.json()),
      fetch(`${API_BASE_URL}/majors`, { headers }).then(r => r.json()),
      fetch(`${API_BASE_URL}/dorm-buildings`, { headers }).then(r => r.json()),
      fetch(`${API_BASE_URL}/users`, { headers }).then(r => r.json())
    ]);

    console.log('📊 最终统计:');
    console.log(`  学院: ${finalStats[0].data.colleges.length} 个`);
    console.log(`  专业: ${finalStats[1].data.majors.length} 个`);
    console.log(`  宿舍楼: ${finalStats[2].data.dormBuildings.length} 个`);
    console.log(`  用户: ${finalStats[3].data.users.length} 个`);

    console.log('\n🎉 测试完成！前端添加功能与数据库完全同步！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testFrontendBackendSync();
