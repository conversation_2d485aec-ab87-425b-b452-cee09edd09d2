import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';
import { User, UserRole } from '../types';
import { API_BASE_URL } from '../config/api';

interface AuthContextType {
  currentUser: User | null;
  login: (email: string, role: UserRole, password?: string) => Promise<void>;
  register: (name: string, email: string, role: UserRole, password?: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const login = useCallback(async (email: string, role: UserRole, password?: string) => {
    setIsLoading(true);
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 在模拟用户中查找匹配的用户
      const user = MOCK_USERS.find(u => 
        u.email === email && 
        u.role === role && 
        (!password || u.password === password)
      );

      if (!user) {
        throw new Error('邮箱、角色或密码不正确');
      }

      // 创建用户对象（不包含密码）
      const userWithoutPassword = { ...user };
      delete userWithoutPassword.password;

      setCurrentUser(userWithoutPassword);
      
      // 保存用户信息到localStorage
      localStorage.setItem('user', JSON.stringify(userWithoutPassword));
      localStorage.setItem('token', 'mock-token-' + Date.now());
      
    } catch (error: any) {
      console.error('登录错误:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (name: string, email: string, role: UserRole, password?: string) => {
    setIsLoading(true);
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 检查邮箱是否已存在
      const existingUser = MOCK_USERS.find(u => u.email === email);
      if (existingUser) {
        throw new Error('该邮箱已被注册');
      }

      // 创建新用户
      const newUser: User = {
        id: 'user-' + Date.now(),
        name,
        email,
        role,
        password: password || 'default123'
      };

      // 根据角色添加额外信息
      if (role === UserRole.STUDENT) {
        newUser.college = '工程学院';
        newUser.major = '计算机科学';
        newUser.dormBuilding = 'A栋 (阿尔法楼)';
        newUser.roomNumber = '103';
        newUser.phone = '13000000004';
        newUser.emergencyContactName = name + '家长';
        newUser.emergencyContactPhone = '13800138001';
      } else if (role === UserRole.DORM_ADMIN) {
        newUser.dormBuilding = 'C栋 (伽马学舍)';
      }

      // 创建用户对象（不包含密码）
      const userWithoutPassword = { ...newUser };
      delete userWithoutPassword.password;

      setCurrentUser(userWithoutPassword);
      
      // 保存用户信息到localStorage
      localStorage.setItem('user', JSON.stringify(userWithoutPassword));
      localStorage.setItem('token', 'mock-token-' + Date.now());
      
    } catch (error: any) {
      console.error('注册错误:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(() => {
    setCurrentUser(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }, []);

  // 初始化时从localStorage恢复用户状态
  React.useEffect(() => {
    const savedUser = localStorage.getItem('user');
    const savedToken = localStorage.getItem('token');
    
    if (savedUser && savedToken) {
      try {
        const user = JSON.parse(savedUser);
        setCurrentUser(user);
      } catch (error) {
        console.error('解析保存的用户信息失败:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    }
  }, []);

  return (
    <AuthContext.Provider value={{ currentUser, login, register, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};