-- 智能宿舍管理系统数据库结构
-- 数据库名: redhat
-- 创建时间: 2024-12-15

-- 使用数据库
USE redhat;

-- 1. 学院表
CREATE TABLE colleges (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 专业表
CREATE TABLE majors (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    college_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
    UNIQUE KEY unique_major_per_college (name, college_id)
);

-- 3. 宿舍楼表
CREATE TABLE dorm_buildings (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    floors INT NOT NULL DEFAULT 1,
    total_rooms INT NOT NULL DEFAULT 0,
    assigned_admin_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 用户表
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
    college VARCHAR(100),
    major VARCHAR(100),
    dorm_building_id VARCHAR(50),
    room_number VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON SET NULL
);

-- 5. 房间表
CREATE TABLE rooms (
    id VARCHAR(50) PRIMARY KEY,
    room_number VARCHAR(20) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    floor INT NOT NULL,
    type ENUM('单人间', '双人间', '四人间', '六人间') NOT NULL DEFAULT '四人间',
    capacity INT NOT NULL DEFAULT 4,
    occupied_beds INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_per_building (room_number, dorm_building_id)
);

-- 6. 床位表
CREATE TABLE beds (
    id VARCHAR(50) PRIMARY KEY,
    room_id VARCHAR(50) NOT NULL,
    bed_number VARCHAR(10) NOT NULL,
    status ENUM('空闲', '已入住') NOT NULL DEFAULT '空闲',
    student_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON SET NULL,
    UNIQUE KEY unique_bed_per_room (bed_number, room_id)
);

-- 7. 维修请求表
CREATE TABLE repair_requests (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    room_number VARCHAR(20) NOT NULL,
    dorm_building VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    image_url VARCHAR(500),
    contact VARCHAR(50) NOT NULL,
    status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') NOT NULL DEFAULT '待处理',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_to VARCHAR(50),
    assigned_to_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON SET NULL
);

-- 8. 维修更新记录表
CREATE TABLE repair_updates (
    id VARCHAR(50) PRIMARY KEY,
    repair_request_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) NOT NULL,
    notes TEXT NOT NULL,
    new_status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_request_id) REFERENCES repair_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 9. 公告表
CREATE TABLE announcements (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id VARCHAR(50) NOT NULL,
    author_name VARCHAR(100) NOT NULL,
    scope ENUM('All', 'College', 'DormBuilding') NOT NULL DEFAULT 'All',
    target_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 10. 水电费账单表
CREATE TABLE utility_bills (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    month VARCHAR(7) NOT NULL, -- YYYY-MM format
    electricity_usage DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    electricity_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    water_usage DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    water_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_paid BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    UNIQUE KEY unique_bill_per_student_month (student_id, month)
);

-- 11. 晚归记录表
CREATE TABLE late_returns (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    reason TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 12. 访客记录表
CREATE TABLE visitors (
    id VARCHAR(50) PRIMARY KEY,
    visitor_name VARCHAR(100) NOT NULL,
    visitor_id_number VARCHAR(50) NOT NULL,
    reason VARCHAR(200),
    entry_time TIMESTAMP NOT NULL,
    exit_time TIMESTAMP NULL,
    visited_student_id VARCHAR(50) NOT NULL,
    visited_student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (visited_student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 13. 违规记录表
CREATE TABLE violations (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 14. 文明宿舍评分表
CREATE TABLE civilized_dorm_scores (
    id VARCHAR(50) PRIMARY KEY,
    dorm_building_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    score INT NOT NULL CHECK (score >= 0 AND score <= 100),
    notes TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 添加索引以提高查询性能
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_dorm_building ON users(dorm_building_id);
CREATE INDEX idx_rooms_building ON rooms(dorm_building_id);
CREATE INDEX idx_beds_room ON beds(room_id);
CREATE INDEX idx_beds_student ON beds(student_id);
CREATE INDEX idx_repair_requests_student ON repair_requests(student_id);
CREATE INDEX idx_repair_requests_status ON repair_requests(status);
CREATE INDEX idx_utility_bills_student ON utility_bills(student_id);
CREATE INDEX idx_utility_bills_month ON utility_bills(month);
CREATE INDEX idx_announcements_scope ON announcements(scope);
CREATE INDEX idx_violations_student ON violations(student_id);
CREATE INDEX idx_visitors_student ON visitors(visited_student_id);
CREATE INDEX idx_late_returns_student ON late_returns(student_id);
CREATE INDEX idx_civilized_scores_room ON civilized_dorm_scores(room_id);

-- 插入初始数据

-- 1. 插入学院数据
INSERT INTO colleges (id, name) VALUES
('college01', '工程学院'),
('college02', '文学院'),
('college03', '理学院'),
('college04', '商学院');

-- 2. 插入专业数据
INSERT INTO majors (id, name, college_id) VALUES
('major01', '计算机科学', 'college01'),
('major02', '软件工程', 'college01'),
('major03', '电子工程', 'college01'),
('major04', '中文文学', 'college02'),
('major05', '英语文学', 'college02'),
('major06', '数学', 'college03'),
('major07', '物理学', 'college03'),
('major08', '工商管理', 'college04'),
('major09', '会计学', 'college04');

-- 3. 插入宿舍楼数据
INSERT INTO dorm_buildings (id, name, floors, total_rooms, assigned_admin_id) VALUES
('bldgA', 'A栋 (阿尔法楼)', 6, 120, NULL),
('bldgB', 'B栋 (贝塔学舍)', 5, 100, NULL),
('bldgC', 'C栋 (伽马学舍)', 4, 80, NULL);

-- 4. 插入用户数据 (密码使用bcrypt加密，这里用明文演示，实际应用中需要加密)
INSERT INTO users (id, name, email, password, phone, role, college, major, dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone) VALUES
-- 系统管理员
('admin01', '系统管理员', '<EMAIL>', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', '13800138000', '系统管理员', NULL, NULL, NULL, NULL, NULL, NULL),

-- 宿舍管理员
('dormadmin01', '张管理员', '<EMAIL>', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', '13800138001', '宿舍管理员', NULL, NULL, 'bldgA', NULL, NULL, NULL),
('dormadmin02', '李管理员', '<EMAIL>', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', '13800138002', '宿舍管理员', NULL, NULL, 'bldgB', NULL, NULL, NULL),

-- 学生
('student01', '王五', '<EMAIL>', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', '13000000001', '学生', '工程学院', '计算机科学', 'bldgA', '101', '王五家长', '13800138101'),
('student02', '赵六', '<EMAIL>', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', '13000000002', '学生', '文学院', '中文文学', 'bldgB', '205', '赵六家长', '13800138102'),
('student03', '孙七', '<EMAIL>', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', '13000000003', '学生', '工程学院', '软件工程', 'bldgA', '102', '孙七家长', '13800138103'),

-- 维修人员
('repair01', '修理工老王', '<EMAIL>', '$2b$10$rOzJqQZJqQZJqQZJqQZJqO', '13900139000', '维修人员', NULL, NULL, NULL, NULL, NULL, NULL);

-- 更新宿舍楼管理员分配
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin01' WHERE id = 'bldgA';
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin02' WHERE id = 'bldgB';

-- 5. 插入房间数据
INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES
('roomA101', '101', 'bldgA', 1, '四人间', 4, 1),
('roomA102', '102', 'bldgA', 1, '四人间', 4, 1),
('roomA103', '103', 'bldgA', 1, '四人间', 4, 0),
('roomA201', '201', 'bldgA', 2, '双人间', 2, 0),
('roomA202', '202', 'bldgA', 2, '双人间', 2, 0),
('roomB205', '205', 'bldgB', 2, '四人间', 4, 1),
('roomB206', '206', 'bldgB', 2, '四人间', 4, 0),
('roomB301', '301', 'bldgB', 3, '六人间', 6, 0),
('roomC101', '101', 'bldgC', 1, '四人间', 4, 0),
('roomC102', '102', 'bldgC', 1, '四人间', 4, 0);

-- 6. 插入床位数据
INSERT INTO beds (id, room_id, bed_number, status, student_id) VALUES
-- A栋101房间床位
('bedA101_1', 'roomA101', '1', '已入住', 'student01'),
('bedA101_2', 'roomA101', '2', '空闲', NULL),
('bedA101_3', 'roomA101', '3', '空闲', NULL),
('bedA101_4', 'roomA101', '4', '空闲', NULL),
-- A栋102房间床位
('bedA102_1', 'roomA102', '1', '已入住', 'student03'),
('bedA102_2', 'roomA102', '2', '空闲', NULL),
('bedA102_3', 'roomA102', '3', '空闲', NULL),
('bedA102_4', 'roomA102', '4', '空闲', NULL),
-- A栋103房间床位
('bedA103_1', 'roomA103', '1', '空闲', NULL),
('bedA103_2', 'roomA103', '2', '空闲', NULL),
('bedA103_3', 'roomA103', '3', '空闲', NULL),
('bedA103_4', 'roomA103', '4', '空闲', NULL),
-- A栋201房间床位
('bedA201_1', 'roomA201', '1', '空闲', NULL),
('bedA201_2', 'roomA201', '2', '空闲', NULL),
-- A栋202房间床位
('bedA202_1', 'roomA202', '1', '空闲', NULL),
('bedA202_2', 'roomA202', '2', '空闲', NULL),
-- B栋205房间床位
('bedB205_1', 'roomB205', '1', '已入住', 'student02'),
('bedB205_2', 'roomB205', '2', '空闲', NULL),
('bedB205_3', 'roomB205', '3', '空闲', NULL),
('bedB205_4', 'roomB205', '4', '空闲', NULL),
-- B栋206房间床位
('bedB206_1', 'roomB206', '1', '空闲', NULL),
('bedB206_2', 'roomB206', '2', '空闲', NULL),
('bedB206_3', 'roomB206', '3', '空闲', NULL),
('bedB206_4', 'roomB206', '4', '空闲', NULL),
-- B栋301房间床位
('bedB301_1', 'roomB301', '1', '空闲', NULL),
('bedB301_2', 'roomB301', '2', '空闲', NULL),
('bedB301_3', 'roomB301', '3', '空闲', NULL),
('bedB301_4', 'roomB301', '4', '空闲', NULL),
('bedB301_5', 'roomB301', '5', '空闲', NULL),
('bedB301_6', 'roomB301', '6', '空闲', NULL),
-- C栋101房间床位
('bedC101_1', 'roomC101', '1', '空闲', NULL),
('bedC101_2', 'roomC101', '2', '空闲', NULL),
('bedC101_3', 'roomC101', '3', '空闲', NULL),
('bedC101_4', 'roomC101', '4', '空闲', NULL),
-- C栋102房间床位
('bedC102_1', 'roomC102', '1', '空闲', NULL),
('bedC102_2', 'roomC102', '2', '空闲', NULL),
('bedC102_3', 'roomC102', '3', '空闲', NULL),
('bedC102_4', 'roomC102', '4', '空闲', NULL);

-- 7. 插入维修请求数据
INSERT INTO repair_requests (id, student_id, student_name, room_number, dorm_building, description, contact, status, submitted_at, assigned_to, assigned_to_name) VALUES
('repair001', 'student01', '王五', '101', 'A栋 (阿尔法楼)', '水龙头漏水，需要维修', '13000000001', '待处理', '2024-12-10 09:00:00', NULL, NULL),
('repair002', 'student02', '赵六', '205', 'B栋 (贝塔学舍)', '电灯不亮，可能是灯泡坏了', '13000000002', '已指派', '2024-12-11 14:30:00', 'repair01', '修理工老王'),
('repair003', 'student03', '孙七', '102', 'A栋 (阿尔法楼)', '门锁坏了，无法正常开关', '13000000003', '维修中', '2024-12-12 16:45:00', 'repair01', '修理工老王');

-- 8. 插入公告数据
INSERT INTO announcements (id, title, content, author_id, author_name, scope, target_id) VALUES
('ann001', '宿舍安全检查通知', '各位同学请注意，本周五将进行宿舍安全检查，请保持宿舍整洁，配合检查工作。', 'admin01', '系统管理员', 'All', NULL),
('ann002', 'A栋停水通知', 'A栋将于明天上午8:00-12:00进行管道维修，期间停水，请提前储水。', 'dormadmin01', '张管理员', 'DormBuilding', 'bldgA'),
('ann003', '工程学院学生会议', '工程学院全体学生请于本周三下午2点在大礼堂参加学院会议。', 'admin01', '系统管理员', 'College', 'college01');

-- 9. 插入水电费账单数据
INSERT INTO utility_bills (id, student_id, room_id, month, electricity_usage, electricity_cost, water_usage, water_cost, total_cost, is_paid) VALUES
('bill001', 'student01', 'roomA101', '2024-11', 45.5, 22.75, 8.2, 16.40, 39.15, TRUE),
('bill002', 'student01', 'roomA101', '2024-12', 52.3, 26.15, 9.1, 18.20, 44.35, FALSE),
('bill003', 'student02', 'roomB205', '2024-11', 38.7, 19.35, 7.5, 15.00, 34.35, TRUE),
('bill004', 'student02', 'roomB205', '2024-12', 41.2, 20.60, 8.0, 16.00, 36.60, FALSE),
('bill005', 'student03', 'roomA102', '2024-11', 47.8, 23.90, 8.8, 17.60, 41.50, TRUE),
('bill006', 'student03', 'roomA102', '2024-12', 49.1, 24.55, 9.3, 18.60, 43.15, FALSE);

-- 10. 插入晚归记录数据
INSERT INTO late_returns (id, student_id, student_name, dorm_building_id, date, time, reason, recorded_by) VALUES
('late001', 'student01', '王五', 'bldgA', '2024-12-08', '23:30:00', '图书馆学习到很晚', 'dormadmin01'),
('late002', 'student03', '孙七', 'bldgA', '2024-12-09', '23:45:00', '参加社团活动', 'dormadmin01'),
('late003', 'student02', '赵六', 'bldgB', '2024-12-10', '23:20:00', '和朋友聚餐', 'dormadmin02');

-- 11. 插入访客记录数据
INSERT INTO visitors (id, visitor_name, visitor_id_number, reason, entry_time, exit_time, visited_student_id, visited_student_name, dorm_building_id, recorded_by) VALUES
('vis001', '访客甲', '123456789012345678', '探亲', '2024-12-07 14:00:00', '2024-12-07 16:00:00', 'student01', '王五', 'bldgA', 'dormadmin01'),
('vis002', '访客乙', '987654321098765432', '送东西', '2024-12-08 10:00:00', '2024-12-08 10:30:00', 'student02', '赵六', 'bldgB', 'dormadmin02'),
('vis003', '访客丙', '456789123456789012', '学术交流', '2024-12-09 15:00:00', NULL, 'student03', '孙七', 'bldgA', 'dormadmin01');

-- 12. 插入违规记录数据
INSERT INTO violations (id, student_id, student_name, dorm_building_id, date, type, description, action_taken, recorded_by) VALUES
('vio001', 'student01', '王五', 'bldgA', '2024-12-05', '噪音扰民', '深夜大声播放音乐', '口头警告', 'dormadmin01'),
('vio002', 'student02', '赵六', 'bldgB', '2024-12-06', '违规电器', '使用大功率电器', '没收电器，书面警告', 'dormadmin02');

-- 13. 插入文明宿舍评分数据
INSERT INTO civilized_dorm_scores (id, dorm_building_id, room_id, date, score, notes, recorded_by) VALUES
('score001', 'bldgA', 'roomA101', '2024-12-01', 85, '整体较好，但需要注意卫生', 'dormadmin01'),
('score002', 'bldgA', 'roomA102', '2024-12-01', 92, '非常整洁，值得表扬', 'dormadmin01'),
('score003', 'bldgB', 'roomB205', '2024-12-01', 78, '需要改善物品摆放', 'dormadmin02'),
('score004', 'bldgA', 'roomA101', '2024-12-08', 88, '有所改善', 'dormadmin01'),
('score005', 'bldgA', 'roomA102', '2024-12-08', 95, '保持优秀', 'dormadmin01');
