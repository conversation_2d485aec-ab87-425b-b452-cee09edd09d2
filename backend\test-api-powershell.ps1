# PowerShell API测试脚本
Write-Host "🧪 测试前端后端数据同步..." -ForegroundColor Green

try {
    # 1. 测试健康检查
    Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/health" -Method GET
    Write-Host "✅ 健康检查成功: $($healthResponse.message)" -ForegroundColor Green

    # 2. 测试登录
    Write-Host "`n2. 测试登录..." -ForegroundColor Yellow
    $loginBody = @{
        email = "<EMAIL>"
        role = "系统管理员"
        password = "password123"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "✅ 登录成功: $($loginResponse.data.user.name)" -ForegroundColor Green
    
    $token = $loginResponse.data.token
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    # 3. 获取当前学院数量
    Write-Host "`n3. 获取当前学院数量..." -ForegroundColor Yellow
    $collegesResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/colleges" -Method GET -Headers $headers
    $initialCount = $collegesResponse.data.colleges.Count
    Write-Host "✅ 当前学院数量: $initialCount" -ForegroundColor Green

    Write-Host "`n📋 现有学院:" -ForegroundColor Cyan
    for ($i = 0; $i -lt $collegesResponse.data.colleges.Count; $i++) {
        $college = $collegesResponse.data.colleges[$i]
        Write-Host "  $($i + 1). $($college.name) (ID: $($college.id))" -ForegroundColor White
    }

    # 4. 添加新学院
    Write-Host "`n4. 添加新学院..." -ForegroundColor Yellow
    $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    $newCollegeName = "PowerShell测试学院_$timestamp"
    
    $collegeBody = @{
        name = $newCollegeName
    } | ConvertTo-Json

    $addResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/colleges" -Method POST -Body $collegeBody -Headers $headers
    Write-Host "✅ 学院添加成功: $($addResponse.data.college.name)" -ForegroundColor Green
    Write-Host "📝 新学院ID: $($addResponse.data.college.id)" -ForegroundColor Cyan

    # 5. 验证学院已添加
    Write-Host "`n5. 验证学院已添加..." -ForegroundColor Yellow
    $verifyResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/colleges" -Method GET -Headers $headers
    $newCount = $verifyResponse.data.colleges.Count
    
    Write-Host "✅ 验证成功: 学院数量从 $initialCount 增加到 $newCount" -ForegroundColor Green
    
    if ($newCount -eq ($initialCount + 1)) {
        Write-Host "🎉 数据同步正常！前端添加的数据已成功保存到数据库" -ForegroundColor Green
    } else {
        Write-Host "❌ 数据同步异常！" -ForegroundColor Red
    }

    # 6. 测试其他功能
    Write-Host "`n6. 测试其他CRUD功能..." -ForegroundColor Yellow
    
    # 测试添加专业
    $majorBody = @{
        name = "PowerShell测试专业_$timestamp"
        college_id = $addResponse.data.college.id
    } | ConvertTo-Json

    try {
        $majorResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/majors" -Method POST -Body $majorBody -Headers $headers
        Write-Host "✅ 专业添加成功: $($majorResponse.data.major.name)" -ForegroundColor Green
    } catch {
        Write-Host "❌ 专业添加失败: $($_.Exception.Message)" -ForegroundColor Red
    }

    # 测试添加宿舍楼
    $buildingBody = @{
        name = "PowerShell测试宿舍楼_$timestamp"
        floors = 5
        total_rooms = 100
    } | ConvertTo-Json

    try {
        $buildingResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/dorm-buildings" -Method POST -Body $buildingBody -Headers $headers
        Write-Host "✅ 宿舍楼添加成功: $($buildingResponse.data.dormBuilding.name)" -ForegroundColor Green
    } catch {
        Write-Host "❌ 宿舍楼添加失败: $($_.Exception.Message)" -ForegroundColor Red
    }

    # 7. 最终统计
    Write-Host "`n7. 最终数据统计..." -ForegroundColor Yellow
    
    $finalColleges = Invoke-RestMethod -Uri "http://localhost:3002/api/colleges" -Method GET -Headers $headers
    $finalMajors = Invoke-RestMethod -Uri "http://localhost:3002/api/majors" -Method GET -Headers $headers
    $finalBuildings = Invoke-RestMethod -Uri "http://localhost:3002/api/dorm-buildings" -Method GET -Headers $headers
    $finalUsers = Invoke-RestMethod -Uri "http://localhost:3002/api/users" -Method GET -Headers $headers

    Write-Host "`n📊 最终统计:" -ForegroundColor Cyan
    Write-Host "  学院: $($finalColleges.data.colleges.Count) 个" -ForegroundColor White
    Write-Host "  专业: $($finalMajors.data.majors.Count) 个" -ForegroundColor White
    Write-Host "  宿舍楼: $($finalBuildings.data.dormBuildings.Count) 个" -ForegroundColor White
    Write-Host "  用户: $($finalUsers.data.users.Count) 个" -ForegroundColor White

    Write-Host "`n🎉 测试完成！前端添加功能与数据库完全同步！" -ForegroundColor Green

} catch {
    Write-Host "❌ 测试失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.Exception)" -ForegroundColor Red
}
