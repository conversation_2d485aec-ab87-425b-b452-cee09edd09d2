# PowerShell脚本测试API
Write-Host "🧪 测试API添加功能..." -ForegroundColor Green

# 1. 测试登录
Write-Host "`n1. 测试登录..." -ForegroundColor Yellow
$loginBody = @{
    email = "<EMAIL>"
    role = "系统管理员"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/auth/login" -Method POST -Body $loginBody -ContentType "application/json"
    Write-Host "✅ 登录成功: $($loginResponse.data.user.name)" -ForegroundColor Green
    $token = $loginResponse.data.token
    
    # 2. 测试添加学院
    Write-Host "`n2. 测试添加学院..." -ForegroundColor Yellow
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    $collegeBody = @{
        name = "PowerShell测试学院_$(Get-Date -Format 'yyyyMMddHHmmss')"
    } | ConvertTo-Json
    
    $collegeResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/colleges" -Method POST -Body $collegeBody -Headers $headers
    Write-Host "✅ 学院添加成功: $($collegeResponse.data.college.name)" -ForegroundColor Green
    Write-Host "📝 学院ID: $($collegeResponse.data.college.id)" -ForegroundColor Cyan
    
    # 3. 测试获取学院列表
    Write-Host "`n3. 测试获取学院列表..." -ForegroundColor Yellow
    $collegesResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/colleges" -Method GET -Headers $headers
    Write-Host "✅ 获取学院列表成功，共 $($collegesResponse.data.colleges.Count) 个学院" -ForegroundColor Green
    
    Write-Host "`n📊 学院列表:" -ForegroundColor Cyan
    foreach ($college in $collegesResponse.data.colleges) {
        Write-Host "  - $($college.name) (ID: $($college.id))" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ 测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API测试完成！" -ForegroundColor Green
