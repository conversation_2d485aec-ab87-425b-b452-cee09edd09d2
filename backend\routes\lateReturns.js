const express = require('express');
const router = express.Router();
const { getLateReturns, createLateReturn, updateLateReturn, deleteLateReturn } = require('../controllers/lateReturnController');
const { authenticateToken, requireDormAdmin } = require('../middleware/auth');

// 获取晚归记录列表
router.get('/', authenticateToken, getLateReturns);

// 创建晚归记录 (需要宿舍管理员或系统管理员权限)
router.post('/', authenticateToken, requireDormAdmin, createLateReturn);

// 更新晚归记录 (需要宿舍管理员或系统管理员权限)
router.put('/:id', authenticateToken, requireDormAdmin, updateLateReturn);

// 删除晚归记录 (需要宿舍管理员或系统管理员权限)
router.delete('/:id', authenticateToken, requireDormAdmin, deleteLateReturn);

module.exports = router;
