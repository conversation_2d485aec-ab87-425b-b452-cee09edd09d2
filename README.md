# 智能宿舍管理系统

一个完整的宿舍管理系统，包含前端React应用和后端Node.js API。

## 项目结构

```
├── qian/                    # 前端React应用
│   ├── src/
│   ├── pages/              # 页面组件
│   ├── components/         # 通用组件
│   ├── contexts/           # React Context
│   ├── config/             # 配置文件
│   └── package.json
├── backend/                # 后端Node.js API
│   ├── controllers/        # 控制器
│   ├── routes/            # 路由
│   ├── middleware/        # 中间件
│   ├── config/            # 配置文件
│   ├── utils/             # 工具函数
│   └── package.json
├── database_schema.sql     # 数据库结构文件
└── README.md
```

## 功能特性

### 用户角色
- **系统管理员**: 管理学院、专业、宿舍楼、用户
- **宿舍管理员**: 管理房间、学生分配、违规记录、访客记录、文明宿舍评分
- **学生**: 查看个人信息、水电费账单、提交维修请求
- **维修人员**: 处理维修任务

### 主要功能模块
1. **用户管理**: 用户注册、登录、权限控制
2. **学院专业管理**: 学院和专业的增删改查
3. **宿舍楼管理**: 宿舍楼信息管理
4. **房间床位管理**: 房间分配、床位管理
5. **维修请求**: 学生提交维修请求，维修人员处理
6. **公告系统**: 发布和管理公告
7. **违规记录**: 记录和管理学生违规行为
8. **访客记录**: 访客登记和管理
9. **晚归记录**: 学生晚归记录
10. **文明宿舍评分**: 宿舍卫生和文明评分
11. **水电费管理**: 水电费账单管理

## 技术栈

### 前端
- React 18
- TypeScript
- Vite
- CSS Modules

### 后端
- Node.js
- Express.js
- MySQL
- JWT认证
- bcryptjs密码加密

## 数据库配置

### 数据库信息
- **数据库名**: redhat
- **用户名**: root
- **密码**: root
- **主机**: localhost
- **端口**: 3306

### 数据库表结构
- colleges (学院表)
- majors (专业表)
- dorm_buildings (宿舍楼表)
- users (用户表)
- rooms (房间表)
- beds (床位表)
- repair_requests (维修请求表)
- repair_updates (维修更新记录表)
- announcements (公告表)
- utility_bills (水电费账单表)
- late_returns (晚归记录表)
- visitors (访客记录表)
- violations (违规记录表)
- civilized_dorm_scores (文明宿舍评分表)

## 安装和运行

### 1. 数据库设置
```bash
# 连接到MySQL
mysql -u root -proot

# 创建数据库
CREATE DATABASE redhat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u root -proot redhat < database_schema.sql
```

### 2. 后端设置
```bash
cd backend
npm install
npm run dev
```
后端服务器将在 http://localhost:3002 启动

### 3. 前端设置
```bash
cd qian
npm install
npm run dev
```
前端应用将在 http://localhost:5173 启动

## API端点

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户

### 学院管理
- `GET /api/colleges` - 获取学院列表
- `POST /api/colleges` - 创建学院
- `PUT /api/colleges/:id` - 更新学院
- `DELETE /api/colleges/:id` - 删除学院

### 专业管理
- `GET /api/majors` - 获取专业列表
- `POST /api/majors` - 创建专业
- `PUT /api/majors/:id` - 更新专业
- `DELETE /api/majors/:id` - 删除专业

### 宿舍楼管理
- `GET /api/dorm-buildings` - 获取宿舍楼列表
- `POST /api/dorm-buildings` - 创建宿舍楼
- `PUT /api/dorm-buildings/:id` - 更新宿舍楼
- `DELETE /api/dorm-buildings/:id` - 删除宿舍楼

### 房间管理
- `GET /api/rooms` - 获取房间列表
- `POST /api/rooms` - 创建房间
- `PUT /api/rooms/:id` - 更新房间
- `DELETE /api/rooms/:id` - 删除房间
- `GET /api/rooms/:id/beds` - 获取房间床位

### 床位管理
- `GET /api/beds` - 获取床位列表
- `PUT /api/beds/:id/assign` - 分配床位
- `PUT /api/beds/:id/release` - 释放床位
- `POST /api/beds/swap` - 调换床位

### 维修请求
- `GET /api/repairs` - 获取维修请求列表
- `POST /api/repairs` - 创建维修请求
- `PUT /api/repairs/:id` - 更新维修请求
- `GET /api/repairs/:id/updates` - 获取维修更新记录

### 公告管理
- `GET /api/announcements` - 获取公告列表
- `POST /api/announcements` - 创建公告
- `PUT /api/announcements/:id` - 更新公告
- `DELETE /api/announcements/:id` - 删除公告

## 默认用户账号

系统已预置以下测试账号：

### 系统管理员
- 邮箱: <EMAIL>
- 密码: password123
- 角色: 系统管理员

### 宿舍管理员
- 邮箱: <EMAIL>
- 密码: password123
- 角色: 宿舍管理员

### 学生
- 邮箱: <EMAIL>
- 密码: password123
- 角色: 学生

### 维修人员
- 邮箱: <EMAIL>
- 密码: password123
- 角色: 维修人员

## 开发说明

### 前端开发模式
前端配置了开发模式，支持邮箱选择自动填充密码，方便测试和开发。

### 数据库连接
后端使用MySQL连接池，支持自动重连和错误处理。

### 安全特性
- JWT令牌认证
- 密码bcrypt加密
- 角色权限控制
- 请求频率限制
- CORS跨域配置

## 部署说明

1. 确保MySQL服务正在运行
2. 创建数据库并导入结构
3. 配置环境变量
4. 启动后端服务
5. 构建并部署前端应用

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查MySQL服务是否启动，用户名密码是否正确
2. **端口占用**: 修改.env文件中的端口配置
3. **CORS错误**: 检查后端CORS配置是否正确
4. **登录失败**: 确认数据库中有对应的用户数据

### 日志查看
- 后端日志: 控制台输出
- 前端日志: 浏览器开发者工具

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
