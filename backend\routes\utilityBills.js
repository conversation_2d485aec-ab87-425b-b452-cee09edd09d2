const express = require('express');
const router = express.Router();
const { getUtilityBills, createUtilityBill, updateUtilityBill, deleteUtilityBill } = require('../controllers/utilityBillController');
const { authenticateToken, requireDormAdmin } = require('../middleware/auth');

// 获取水电费账单列表
router.get('/', authenticateToken, getUtilityBills);

// 创建水电费账单 (需要宿舍管理员或系统管理员权限)
router.post('/', authenticateToken, requireDormAdmin, createUtilityBill);

// 更新水电费账单 (需要宿舍管理员或系统管理员权限)
router.put('/:id', authenticateToken, requireDormAdmin, updateUtilityBill);

// 删除水电费账单 (需要宿舍管理员或系统管理员权限)
router.delete('/:id', authenticateToken, requireDormAdmin, deleteUtilityBill);

module.exports = router;
