// 测试添加学院功能
const API_BASE_URL = 'http://localhost:3002/api';

async function testAddCollege() {
  console.log('🧪 测试添加学院功能...\n');

  try {
    // 1. 先登录获取token
    console.log('1. 登录系统管理员...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        role: '系统管理员',
        password: 'password123'
      }),
    });
    
    if (!loginResponse.ok) {
      throw new Error('登录失败');
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ 登录成功:', loginData.data.user.name);
    
    const token = loginData.data.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 添加新学院
    console.log('\n2. 添加新学院...');
    const collegeResponse = await fetch(`${API_BASE_URL}/colleges`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: '测试学院_' + Date.now()
      })
    });
    
    if (collegeResponse.ok) {
      const collegeData = await collegeResponse.json();
      console.log('✅ 学院添加成功:', collegeData.data.college.name);
      console.log('📝 学院ID:', collegeData.data.college.id);
      return collegeData.data.college;
    } else {
      const errorData = await collegeResponse.json();
      console.log('❌ 学院添加失败:', errorData.message);
      return null;
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    return null;
  }
}

// 运行测试
testAddCollege().then(result => {
  if (result) {
    console.log('\n🎉 测试完成！请检查数据库中是否有新添加的学院。');
  }
});
