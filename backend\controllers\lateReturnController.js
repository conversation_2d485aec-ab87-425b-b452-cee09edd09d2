const { query } = require('../config/database');

/**
 * 获取晚归记录列表
 */
async function getLateReturns(req, res) {
  try {
    const { studentId, dormBuildingId, date } = req.query;
    const userRole = req.user.role;
    const userId = req.user.id;
    
    let sql = `
      SELECT lr.*, u.name as student_name, db.name as dorm_building_name, ru.name as recorded_by_name
      FROM late_returns lr
      LEFT JOIN users u ON lr.student_id = u.id
      LEFT JOIN dorm_buildings db ON lr.dorm_building_id = db.id
      LEFT JOIN users ru ON lr.recorded_by = ru.id
      WHERE 1=1
    `;
    const params = [];

    // 根据用户角色过滤数据
    if (userRole === '学生') {
      sql += ' AND lr.student_id = ?';
      params.push(userId);
    } else if (userRole === '宿舍管理员') {
      // 宿舍管理员只能看到自己管理的宿舍楼的晚归记录
      const userInfo = await query(
        'SELECT dorm_building_id FROM users WHERE id = ?',
        [userId]
      );
      
      if (userInfo.length > 0) {
        sql += ' AND lr.dorm_building_id = ?';
        params.push(userInfo[0].dorm_building_id);
      }
    }

    // 其他过滤条件
    if (studentId) {
      sql += ' AND lr.student_id = ?';
      params.push(studentId);
    }

    if (dormBuildingId) {
      sql += ' AND lr.dorm_building_id = ?';
      params.push(dormBuildingId);
    }

    if (date) {
      sql += ' AND lr.date = ?';
      params.push(date);
    }

    sql += ' ORDER BY lr.date DESC, lr.time DESC';

    const lateReturns = await query(sql, params);

    res.json({
      success: true,
      data: {
        lateReturns: lateReturns
      }
    });
  } catch (error) {
    console.error('获取晚归记录列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 创建晚归记录
 */
async function createLateReturn(req, res) {
  try {
    const { student_id, dorm_building_id, date, time, reason } = req.body;
    const recordedBy = req.user.id;

    // 验证输入
    if (!student_id || !dorm_building_id || !date || !time) {
      return res.status(400).json({
        success: false,
        message: '学生ID、宿舍楼ID、日期和时间都是必填项'
      });
    }

    // 检查学生是否存在
    const students = await query(
      'SELECT id, name FROM users WHERE id = ? AND role = ?',
      [student_id, '学生']
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '学生不存在'
      });
    }

    // 检查宿舍楼是否存在
    const buildings = await query(
      'SELECT id FROM dorm_buildings WHERE id = ?',
      [dorm_building_id]
    );

    if (buildings.length === 0) {
      return res.status(404).json({
        success: false,
        message: '宿舍楼不存在'
      });
    }

    // 检查是否已存在该学生该日期的晚归记录
    const existingRecords = await query(
      'SELECT id FROM late_returns WHERE student_id = ? AND date = ?',
      [student_id, date]
    );

    if (existingRecords.length > 0) {
      return res.status(409).json({
        success: false,
        message: '该学生该日期的晚归记录已存在'
      });
    }

    // 生成晚归记录ID
    const lateReturnId = 'late_' + Date.now();

    // 插入新晚归记录
    await query(
      `INSERT INTO late_returns (
        id, student_id, student_name, dorm_building_id, 
        date, time, reason, recorded_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        lateReturnId, student_id, students[0].name, dorm_building_id,
        date, time, reason || null, recordedBy
      ]
    );

    // 获取新创建的晚归记录信息
    const newLateReturns = await query(
      `SELECT lr.*, u.name as student_name, db.name as dorm_building_name, ru.name as recorded_by_name
       FROM late_returns lr
       LEFT JOIN users u ON lr.student_id = u.id
       LEFT JOIN dorm_buildings db ON lr.dorm_building_id = db.id
       LEFT JOIN users ru ON lr.recorded_by = ru.id
       WHERE lr.id = ?`,
      [lateReturnId]
    );

    res.status(201).json({
      success: true,
      message: '晚归记录创建成功',
      data: {
        lateReturn: newLateReturns[0]
      }
    });
  } catch (error) {
    console.error('创建晚归记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 更新晚归记录
 */
async function updateLateReturn(req, res) {
  try {
    const { id } = req.params;
    const { time, reason } = req.body;
    const userRole = req.user.role;
    const userId = req.user.id;

    // 检查晚归记录是否存在
    const existingRecords = await query(
      'SELECT * FROM late_returns WHERE id = ?',
      [id]
    );

    if (existingRecords.length === 0) {
      return res.status(404).json({
        success: false,
        message: '晚归记录不存在'
      });
    }

    const lateReturn = existingRecords[0];

    // 权限检查：只有记录者或系统管理员可以修改
    if (lateReturn.recorded_by !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能修改自己记录的晚归记录'
      });
    }

    // 更新晚归记录信息
    await query(
      `UPDATE late_returns SET 
        time = COALESCE(?, time),
        reason = ?,
        updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [time, reason || null, id]
    );

    // 获取更新后的晚归记录信息
    const updatedRecords = await query(
      `SELECT lr.*, u.name as student_name, db.name as dorm_building_name, ru.name as recorded_by_name
       FROM late_returns lr
       LEFT JOIN users u ON lr.student_id = u.id
       LEFT JOIN dorm_buildings db ON lr.dorm_building_id = db.id
       LEFT JOIN users ru ON lr.recorded_by = ru.id
       WHERE lr.id = ?`,
      [id]
    );

    res.json({
      success: true,
      message: '晚归记录更新成功',
      data: {
        lateReturn: updatedRecords[0]
      }
    });
  } catch (error) {
    console.error('更新晚归记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

/**
 * 删除晚归记录
 */
async function deleteLateReturn(req, res) {
  try {
    const { id } = req.params;
    const userRole = req.user.role;
    const userId = req.user.id;

    // 检查晚归记录是否存在
    const existingRecords = await query(
      'SELECT * FROM late_returns WHERE id = ?',
      [id]
    );

    if (existingRecords.length === 0) {
      return res.status(404).json({
        success: false,
        message: '晚归记录不存在'
      });
    }

    const lateReturn = existingRecords[0];

    // 权限检查：只有记录者或系统管理员可以删除
    if (lateReturn.recorded_by !== userId && userRole !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足，只能删除自己记录的晚归记录'
      });
    }

    // 删除晚归记录
    await query('DELETE FROM late_returns WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '晚归记录删除成功'
    });
  } catch (error) {
    console.error('删除晚归记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
}

module.exports = {
  getLateReturns,
  createLateReturn,
  updateLateReturn,
  deleteLateReturn
};
