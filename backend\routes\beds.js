const express = require('express');
const router = express.Router();
const { getBeds, assignBed, releaseBed, swapBeds } = require('../controllers/bedController');
const { authenticateToken, requireDormAdmin } = require('../middleware/auth');

// 获取床位列表
router.get('/', authenticateToken, getBeds);

// 分配床位给学生 (需要宿舍管理员或系统管理员权限)
router.put('/:id/assign', authenticateToken, requireDormAdmin, assignBed);

// 释放床位 (需要宿舍管理员或系统管理员权限)
router.put('/:id/release', authenticateToken, requireDormAdmin, releaseBed);

// 调换床位 (需要宿舍管理员或系统管理员权限)
router.post('/swap', authenticateToken, requireDormAdmin, swapBeds);

module.exports = router;
